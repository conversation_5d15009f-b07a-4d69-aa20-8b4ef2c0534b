package command

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"sync/atomic"

	"github.com/penwyp/hydra/apps/hydra/pkg/ebook"
	"github.com/penwyp/hydra/shared/log"
	"github.com/spf13/cobra"
	"go.uber.org/zap"
)

func NewEbookClassifierCommand() *cobra.Command {
	var cmd = &cobra.Command{
		Use:   "ebook-classify",
		Short: "扫描电子书并根据LLM进行自动分类",
		Long: `扫描指定目录中的电子书文件，根据文件名检索书本信息，
然后使用LLM对书本进行分类，并将文件移动到相应的分类目录中。

支持的电子书格式：
- PDF (.pdf)
- EPUB (.epub)
- MOBI (.mobi)
- AZW (.azw, .azw3)
- TXT (.txt)

索引功能：
- 自动维护已处理文件的索引，避免重复处理
- 索引文件保存在目标目录的 .ebook_enhanced_index.json 中
- 支持从目标目录重新构建索引

示例用法：
  # 基本分类
  hydra ebook-classify -s /path/to/source -t /path/to/target --llm-api-key your-api-key

  # 试运行模式（不实际移动文件）
  hydra ebook-classify -s /books/unsorted -t /books/sorted --dry-run

  # 重新构建索引（基于目标目录的现有分类）
  hydra ebook-classify -t /books/sorted --rebuild-index

  # 显示索引统计信息
  hydra ebook-classify -t /books/sorted --show-stats`,
		Run: doEbookClassify,
	}

	cmd.PersistentFlags().StringVarP(&logLevel, "logLevel", "l", "info", "日志级别 (info/debug)")
	cmd.PersistentFlags().StringVarP(&sourceDir, "source", "s", "", "源目录路径（必需）")
	cmd.PersistentFlags().StringVarP(&targetDir, "target", "t", "", "目标分类目录路径（必需）")
	cmd.PersistentFlags().StringVar(&llmApiKey, "llm-api-key", "", "LLM API密钥")
	cmd.PersistentFlags().StringVar(&llmApiUrl, "llm-api-url", "https://ark.cn-beijing.volces.com/api/v3/chat/completions", "LLM API地址")
	cmd.PersistentFlags().StringVar(&llmModel, "llm-model", "deepseek-v3-1-250821", "LLM模型名称")
	cmd.PersistentFlags().BoolVar(&dryRun, "dry-run", false, "试运行模式，不实际移动文件")
	cmd.PersistentFlags().BoolVar(&rebuildIndex, "rebuild-index", false, "使用目标目录的分类和电子书重新构建索引")
	cmd.PersistentFlags().BoolVar(&showStats, "show-stats", false, "显示索引统计信息后退出")

	// 标记必需参数
	cmd.MarkPersistentFlagRequired("source")
	cmd.MarkPersistentFlagRequired("target")

	return cmd
}

func doEbookClassify(cmd *cobra.Command, args []string) {
	log.InitLogger(log.LoggerOption{LogLevel: logLevel})

	// 验证参数
	if sourceDir == "" || targetDir == "" {
		log.Logger.Fatal("源目录和目标目录都是必需参数")
		return
	}

	// 自动读取环境变量API密钥
	if llmApiKey == "" {
		if envKey := os.Getenv("HYDRA_LLM_API_KEY"); envKey != "" {
			llmApiKey = envKey
			log.Logger.Info("从环境变量读取LLM API密钥")
		}
	}

	// 提前验证API密钥
	if llmApiKey == "" {
		log.Logger.Fatal("LLM API密钥未配置，请设置 --llm-api-key 参数或 HYDRA_LLM_API_KEY 环境变量")
		return
	}

	// 验证目录存在性
	if _, err := os.Stat(sourceDir); os.IsNotExist(err) {
		log.Logger.Fatal("源目录不存在", zap.String("path", sourceDir))
		return
	}

	// 创建目标目录（如果不存在）
	if err := os.MkdirAll(targetDir, 0755); err != nil {
		log.Logger.Fatal("创建目标目录失败", zap.String("path", targetDir), zap.Error(err))
		return
	}

	log.Logger.Info("开始电子书分类任务",
		zap.String("source", sourceDir),
		zap.String("target", targetDir),
		zap.Bool("dryRun", dryRun),
		zap.Bool("rebuildIndex", rebuildIndex),
		zap.String("llmModel", llmModel))

	// 0. 初始化索引管理器
	indexManager := ebook.NewIndexManager(targetDir)
	if err := indexManager.LoadIndex(); err != nil {
		log.Logger.Error("加载索引失败", zap.Error(err))
		// 继续执行，但不使用索引功能
	}

	// 如果只是显示统计信息，则显示后退出
	if showStats {
		indexManager.PrintStats()
		return
	}

	// 如果是重建索引模式，则扫描目标目录重建索引
	if rebuildIndex {
		if err := rebuildIndexFromTarget(targetDir, indexManager); err != nil {
			log.Logger.Fatal("重建索引失败", zap.Error(err))
			return
		}
		log.Logger.Info("索引重建完成")
		indexManager.PrintStats()
		return
	}

	// 1. 扫描电子书文件
	scanner := ebook.NewScanner(sourceDir)
	files, err := scanner.ScanDirectory()
	if err != nil {
		log.Logger.Fatal("扫描目录失败", zap.Error(err))
		return
	}

	if len(files) == 0 {
		log.Logger.Info("未找到电子书文件")
		return
	}

	log.Logger.Info("扫描完成", zap.Int("fileCount", len(files)))

	// 2. 开始流水线处理
	log.Logger.Info("开始流水线处理电子书文件...")

	// 统计变量（使用原子操作保证线程安全）
	var processedCount, skippedFromIndex, successCount, failureCount int64
	var wg sync.WaitGroup

	// 创建处理组件
	metadataRetriever := ebook.NewMetadataRetriever()
	if googleApiKey := os.Getenv("GOOGLE_BOOKS_API_KEY"); googleApiKey != "" {
		metadataRetriever.SetAPIKey("google", googleApiKey)
	}

	classifier := ebook.NewLLMClassifier(llmApiKey, llmApiUrl, llmModel)
	organizer := ebook.NewFileOrganizer(ebook.OrganizeOptions{
		SourceDir:   sourceDir,
		TargetDir:   targetDir,
		DefaultMove: false,
		DryRun:      dryRun,
	})

	// 处理每个文件
	for i, file := range files {
		log.Logger.Info("处理文件",
			zap.Int("current", i+1),
			zap.Int("total", len(files)),
			zap.String("fileName", file.Name))

		// 使用智能索引查找，避免重复处理
		findResult := indexManager.FindWithValidation(file, nil)
		if findResult.Entry != nil {
			// 找到匹配的条目，直接移动文件到对应分类目录
			classification := &ebook.ClassificationResult{
				Category:   findResult.Entry.Category,
				Confidence: findResult.Entry.Confidence,
				Reason:     fmt.Sprintf("索引匹配(%s)", findResult.MatchType),
			}

			wg.Add(1)
			go func(f ebook.EbookFile, c *ebook.ClassificationResult) {
				defer wg.Done()
				if targetPath, err := organizer.MoveFileToCategory(f.Path, c.Category); err != nil {
					log.Logger.Error("移动文件失败",
						zap.String("fileName", f.Name),
						zap.Error(err))
					atomic.AddInt64(&failureCount, 1)
				} else {
					log.Logger.Info("文件移动成功（来自索引）",
						zap.String("fileName", f.Name),
						zap.String("category", c.Category),
						zap.String("matchType", findResult.MatchType),
						zap.String("targetPath", targetPath))
					atomic.AddInt64(&successCount, 1)
				}
			}(file, classification)

			atomic.AddInt64(&skippedFromIndex, 1)
			continue
		}

		// 需要完整处理的文件
		wg.Add(1)
		go func(f ebook.EbookFile) {
			defer wg.Done()
			processEbookFile(f, metadataRetriever, classifier, organizer, indexManager, &successCount, &failureCount)
		}(file)

		atomic.AddInt64(&processedCount, 1)
	}

	// 等待所有goroutine完成
	wg.Wait()

	// 输出最终结果
	fmt.Printf("\n=== 电子书分类完成 ===\n")
	fmt.Printf("总文件数: %d\n", len(files))
	fmt.Printf("成功处理: %d\n", atomic.LoadInt64(&successCount))
	fmt.Printf("处理失败: %d\n", atomic.LoadInt64(&failureCount))
	fmt.Printf("从索引跳过: %d\n", atomic.LoadInt64(&skippedFromIndex))

	// 显示索引统计信息
	if !dryRun {
		indexManager.PrintStats()
	}

	if dryRun {
		fmt.Printf("\n注意: 这是试运行模式，文件未实际移动，索引未更新\n")
	}

	log.Logger.Info("电子书分类任务完成")
}

// processEbookFile 处理单个电子书文件
func processEbookFile(
	file ebook.EbookFile,
	metadataRetriever *ebook.MetadataRetriever,
	classifier *ebook.LLMClassifier,
	organizer *ebook.FileOrganizer,
	indexManager *ebook.IndexManager,
	successCount, failureCount *int64,
) {
	// 1. 检索书本信息（使用增强版，支持文件元数据提取）
	bookInfo, err := metadataRetriever.RetrieveMetadataWithPath(file.Name, file.Path)
	if err != nil {
		log.Logger.Error("检索书本信息失败",
			zap.String("fileName", file.Name),
			zap.String("filePath", file.Path),
			zap.Error(err))
		atomic.AddInt64(failureCount, 1)
		return
	}

	// 2. 使用智能索引查找
	findResult := indexManager.FindWithValidation(file, bookInfo)

	var classification *ebook.ClassificationResult
	var targetPath string

	if findResult.Entry != nil {
		// 找到匹配的条目，直接使用已有分类
		log.Logger.Info("使用索引中的分类结果",
			zap.String("fileName", file.Name),
			zap.String("matchType", findResult.MatchType),
			zap.Float64("confidence", findResult.Confidence),
			zap.String("category", findResult.Entry.Category))

		classification = &ebook.ClassificationResult{
			Category:   findResult.Entry.Category,
			Confidence: findResult.Entry.Confidence,
			Reason:     fmt.Sprintf("索引匹配(%s, 置信度: %.2f)", findResult.MatchType, findResult.Confidence),
		}
	} else if len(findResult.Conflicts) > 0 {
		// 有冲突，记录警告但继续进行LLM分类
		log.Logger.Warn("发现索引冲突，将进行LLM重新分类",
			zap.String("fileName", file.Name),
			zap.Int("conflictCount", len(findResult.Conflicts)))

		// 进行LLM分类
		classification, err = classifier.ClassifyBook(bookInfo)
		if err != nil {
			log.Logger.Warn("跳过文件：LLM分类失败",
				zap.String("fileName", file.Name),
				zap.Error(err))
			atomic.AddInt64(failureCount, 1)
			return
		}
	} else {
		// 没有找到匹配项，进行LLM分类
		classification, err = classifier.ClassifyBook(bookInfo)
		if err != nil {
			log.Logger.Warn("跳过文件：LLM分类失败",
				zap.String("fileName", file.Name),
				zap.Error(err))
			atomic.AddInt64(failureCount, 1)
			return
		}
	}

	// 3. 移动文件
	targetPath, err = moveFileToCategory(file.Path, classification.Category, organizer)
	if err != nil {
		log.Logger.Error("移动文件失败",
			zap.String("fileName", file.Name),
			zap.String("category", classification.Category),
			zap.Error(err))
		atomic.AddInt64(failureCount, 1)
		return
	}

	// 4. 更新索引
	indexManager.AddEntry(file, bookInfo, classification, targetPath)
	if err := indexManager.SaveIndex(); err != nil {
		log.Logger.Error("保存索引失败", zap.Error(err))
	}

	log.Logger.Info("文件处理完成",
		zap.String("fileName", file.Name),
		zap.String("category", classification.Category),
		zap.String("targetPath", targetPath))

	atomic.AddInt64(successCount, 1)
}

// moveFileToCategory 移动文件到指定分类目录
func moveFileToCategory(sourcePath, category string, organizer *ebook.FileOrganizer) (string, error) {
	return organizer.MoveFileToCategory(sourcePath, category)
}

// rebuildIndexFromTarget 从目标目录重建索引
func rebuildIndexFromTarget(targetDir string, indexManager *ebook.IndexManager) error {
	log.Logger.Info("开始从目标目录重建索引", zap.String("targetDir", targetDir))

	// 创建新的空索引
	newIndexManager := ebook.NewIndexManager(targetDir)
	if err := newIndexManager.LoadIndex(); err != nil {
		return fmt.Errorf("初始化新索引失败: %w", err)
	}

	// 创建元数据检索器
	metadataRetriever := ebook.NewMetadataRetriever()
	if googleApiKey := os.Getenv("GOOGLE_BOOKS_API_KEY"); googleApiKey != "" {
		metadataRetriever.SetAPIKey("google", googleApiKey)
	}

	var totalFiles, processedFiles, errorFiles int64

	// 遍历目标目录的所有分类子目录
	err := filepath.Walk(targetDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 跳过目录和隐藏文件
		if info.IsDir() || strings.HasPrefix(info.Name(), ".") {
			return nil
		}

		// 检查是否是电子书文件
		ext := strings.ToLower(filepath.Ext(info.Name()))
		if !isEbookFile(ext) {
			return nil
		}

		atomic.AddInt64(&totalFiles, 1)

		// 确定分类（基于文件所在的目录）
		relPath, err := filepath.Rel(targetDir, path)
		if err != nil {
			log.Logger.Warn("无法获取相对路径", zap.String("path", path), zap.Error(err))
			atomic.AddInt64(&errorFiles, 1)
			return nil
		}

		// 获取分类名称（第一级目录名）
		pathParts := strings.Split(relPath, string(filepath.Separator))
		if len(pathParts) < 2 {
			// 文件直接在目标目录下，跳过
			return nil
		}
		category := pathParts[0]

		// 创建 EbookFile 对象
		nameWithoutExt := strings.TrimSuffix(info.Name(), ext)
		file := ebook.EbookFile{
			Name:      nameWithoutExt,
			Path:      path,
			Size:      info.Size(),
			Extension: ext,
		}

		// 检索书本元数据
		bookInfo, err := metadataRetriever.RetrieveMetadataWithPath(file.Name, file.Path)
		if err != nil {
			log.Logger.Debug("检索元数据失败，使用基本信息",
				zap.String("fileName", file.Name),
				zap.Error(err))
			// 使用基本信息
			bookInfo = &ebook.BookInfo{
				Title:  file.Name,
				Author: "",
			}
		}

		// 创建分类结果
		classification := &ebook.ClassificationResult{
			Category:   category,
			Confidence: 1.0,
			Reason:     "从目标目录结构重建",
		}

		// 添加到新索引
		newIndexManager.AddEntry(file, bookInfo, classification, path)

		atomic.AddInt64(&processedFiles, 1)

		if processedFiles%100 == 0 {
			log.Logger.Info("重建索引进度",
				zap.Int64("processed", processedFiles),
				zap.Int64("total", totalFiles))
		}

		return nil
	})

	if err != nil {
		return fmt.Errorf("遍历目标目录失败: %w", err)
	}

	// 保存新索引
	if err := newIndexManager.SaveIndex(); err != nil {
		return fmt.Errorf("保存重建的索引失败: %w", err)
	}

	log.Logger.Info("索引重建完成",
		zap.Int64("totalFiles", totalFiles),
		zap.Int64("processedFiles", processedFiles),
		zap.Int64("errorFiles", errorFiles))

	// 更新原索引管理器的引用
	*indexManager = *newIndexManager

	return nil
}

// isEbookFile 检查文件扩展名是否为电子书格式
func isEbookFile(ext string) bool {
	ebookExts := []string{".pdf", ".epub", ".mobi", ".azw", ".azw3", ".txt"}
	for _, ebookExt := range ebookExts {
		if ext == ebookExt {
			return true
		}
	}
	return false
}
