package command

import (
	"fmt"
	"os"
	"sync"
	"sync/atomic"

	"github.com/penwyp/hydra/apps/hydra/pkg/ebook"
	"github.com/penwyp/hydra/shared/log"
	"github.com/spf13/cobra"
	"go.uber.org/zap"
)

func NewEbookClassifierCommand() *cobra.Command {
	var cmd = &cobra.Command{
		Use:   "ebook-classify",
		Short: "扫描电子书并根据LLM进行自动分类",
		Long: `扫描指定目录中的电子书文件，根据文件名检索书本信息，
然后使用LLM对书本进行分类，并将文件移动到相应的分类目录中。

支持的电子书格式：
- PDF (.pdf)
- EPUB (.epub)
- MOBI (.mobi)
- AZW (.azw, .azw3)
- TXT (.txt)

索引功能：
- 自动维护已处理文件的索引，避免重复处理
- 索引文件保存在目标目录的 .ebook_index.json 中
- 支持强制重新处理已索引的文件

示例用法：
  # 基本分类
  hydra ebook-classify -s /path/to/source -t /path/to/target --llm-api-key your-api-key

  # 试运行模式（不实际移动文件）
  hydra ebook-classify -s /books/unsorted -t /books/sorted --dry-run

  # 强制重新处理所有文件（忽略索引）
  hydra ebook-classify -s /books/unsorted -t /books/sorted --force-reprocess

  # 显示索引统计信息
  hydra ebook-classify -t /books/sorted --show-stats`,
		Run: doEbookClassify,
	}

	cmd.PersistentFlags().StringVarP(&logLevel, "logLevel", "l", "info", "日志级别 (info/debug)")
	cmd.PersistentFlags().StringVarP(&sourceDir, "source", "s", "", "源目录路径（必需）")
	cmd.PersistentFlags().StringVarP(&targetDir, "target", "t", "", "目标分类目录路径（必需）")
	cmd.PersistentFlags().StringVar(&llmApiKey, "llm-api-key", "", "LLM API密钥")
	cmd.PersistentFlags().StringVar(&llmApiUrl, "llm-api-url", "https://ark.cn-beijing.volces.com/api/v3/chat/completions", "LLM API地址")
	cmd.PersistentFlags().StringVar(&llmModel, "llm-model", "deepseek-v3-1-250821", "LLM模型名称")
	cmd.PersistentFlags().BoolVar(&dryRun, "dry-run", false, "试运行模式，不实际移动文件")
	cmd.PersistentFlags().BoolVar(&forceReprocess, "force-reprocess", false, "强制重新处理已索引的文件")
	cmd.PersistentFlags().BoolVar(&showStats, "show-stats", false, "显示索引统计信息后退出")

	// 标记必需参数
	cmd.MarkPersistentFlagRequired("source")
	cmd.MarkPersistentFlagRequired("target")

	return cmd
}

func doEbookClassify(cmd *cobra.Command, args []string) {
	log.InitLogger(log.LoggerOption{LogLevel: logLevel})

	// 验证参数
	if sourceDir == "" || targetDir == "" {
		log.Logger.Fatal("源目录和目标目录都是必需参数")
		return
	}

	// 自动读取环境变量API密钥
	if llmApiKey == "" {
		if envKey := os.Getenv("HYDRA_LLM_API_KEY"); envKey != "" {
			llmApiKey = envKey
			log.Logger.Info("从环境变量读取LLM API密钥")
		}
	}

	// 提前验证API密钥
	if llmApiKey == "" {
		log.Logger.Fatal("LLM API密钥未配置，请设置 --llm-api-key 参数或 HYDRA_LLM_API_KEY 环境变量")
		return
	}

	// 验证目录存在性
	if _, err := os.Stat(sourceDir); os.IsNotExist(err) {
		log.Logger.Fatal("源目录不存在", zap.String("path", sourceDir))
		return
	}

	// 创建目标目录（如果不存在）
	if err := os.MkdirAll(targetDir, 0755); err != nil {
		log.Logger.Fatal("创建目标目录失败", zap.String("path", targetDir), zap.Error(err))
		return
	}

	log.Logger.Info("开始电子书分类任务",
		zap.String("source", sourceDir),
		zap.String("target", targetDir),
		zap.Bool("dryRun", dryRun),
		zap.Bool("forceReprocess", forceReprocess),
		zap.String("llmModel", llmModel))

	// 0. 初始化索引管理器
	indexManager := ebook.NewIndexManager(targetDir)
	if err := indexManager.LoadIndex(); err != nil {
		log.Logger.Error("加载索引失败", zap.Error(err))
		// 继续执行，但不使用索引功能
	}

	// 如果只是显示统计信息，则显示后退出
	if showStats {
		indexManager.PrintStats()
		return
	}

	// 1. 扫描电子书文件
	scanner := ebook.NewScanner(sourceDir)
	files, err := scanner.ScanDirectory()
	if err != nil {
		log.Logger.Fatal("扫描目录失败", zap.Error(err))
		return
	}

	if len(files) == 0 {
		log.Logger.Info("未找到电子书文件")
		return
	}

	log.Logger.Info("扫描完成", zap.Int("fileCount", len(files)))

	// 2. 开始流水线处理
	log.Logger.Info("开始流水线处理电子书文件...")

	// 统计变量（使用原子操作保证线程安全）
	var processedCount, skippedFromIndex, successCount, failureCount int64
	var wg sync.WaitGroup

	// 创建处理组件
	metadataRetriever := ebook.NewMetadataRetriever()
	if googleApiKey := os.Getenv("GOOGLE_BOOKS_API_KEY"); googleApiKey != "" {
		metadataRetriever.SetAPIKey("google", googleApiKey)
	}

	classifier := ebook.NewLLMClassifier(llmApiKey, llmApiUrl, llmModel)
	organizer := ebook.NewFileOrganizer(ebook.OrganizeOptions{
		SourceDir:   sourceDir,
		TargetDir:   targetDir,
		DefaultMove: false,
		DryRun:      dryRun,
	})

	// 处理每个文件
	for i, file := range files {
		log.Logger.Info("处理文件",
			zap.Int("current", i+1),
			zap.Int("total", len(files)),
			zap.String("fileName", file.Name))

		// 检查是否已在索引中（如果不强制重新处理）
		if !forceReprocess {
			if category, exists := indexManager.FindByFileName(file.Name); exists {
				// 直接移动文件到对应分类目录
				classification := &ebook.ClassificationResult{
					Category:   category,
					Confidence: 1.0,
					Reason:     "从索引中获取的分类",
				}

				wg.Add(1)
				go func(f ebook.EbookFile, c *ebook.ClassificationResult) {
					defer wg.Done()
					if targetPath, err := organizer.MoveFileToCategory(f.Path, c.Category); err != nil {
						log.Logger.Error("移动文件失败",
							zap.String("fileName", f.Name),
							zap.Error(err))
						atomic.AddInt64(&failureCount, 1)
					} else {
						log.Logger.Info("文件移动成功（来自索引）",
							zap.String("fileName", f.Name),
							zap.String("category", c.Category),
							zap.String("targetPath", targetPath))
						atomic.AddInt64(&successCount, 1)
					}
				}(file, classification)

				atomic.AddInt64(&skippedFromIndex, 1)
				continue
			}
		}

		// 需要完整处理的文件
		wg.Add(1)
		go func(f ebook.EbookFile) {
			defer wg.Done()
			processEbookFile(f, metadataRetriever, classifier, organizer, indexManager, &successCount, &failureCount)
		}(file)

		atomic.AddInt64(&processedCount, 1)
	}

	// 等待所有goroutine完成
	wg.Wait()

	// 输出最终结果
	fmt.Printf("\n=== 电子书分类完成 ===\n")
	fmt.Printf("总文件数: %d\n", len(files))
	fmt.Printf("成功处理: %d\n", atomic.LoadInt64(&successCount))
	fmt.Printf("处理失败: %d\n", atomic.LoadInt64(&failureCount))
	fmt.Printf("从索引跳过: %d\n", atomic.LoadInt64(&skippedFromIndex))

	// 显示索引统计信息
	if !dryRun {
		indexManager.PrintStats()
	}

	if dryRun {
		fmt.Printf("\n注意: 这是试运行模式，文件未实际移动，索引未更新\n")
	}

	log.Logger.Info("电子书分类任务完成")
}

// processEbookFile 处理单个电子书文件
func processEbookFile(
	file ebook.EbookFile,
	metadataRetriever *ebook.MetadataRetriever,
	classifier *ebook.LLMClassifier,
	organizer *ebook.FileOrganizer,
	indexManager *ebook.IndexManager,
	successCount, failureCount *int64,
) {
	// 1. 检索书本信息（使用增强版，支持文件元数据提取）
	bookInfo, err := metadataRetriever.RetrieveMetadataWithPath(file.Name, file.Path)
	if err != nil {
		log.Logger.Error("检索书本信息失败",
			zap.String("fileName", file.Name),
			zap.String("filePath", file.Path),
			zap.Error(err))
		atomic.AddInt64(failureCount, 1)
		return
	}

	// 2. LLM分类
	classification, err := classifier.ClassifyBook(bookInfo)
	if err != nil {
		log.Logger.Warn("跳过文件：LLM分类失败",
			zap.String("fileName", file.Name),
			zap.Error(err))
		atomic.AddInt64(failureCount, 1)
		return
	}

	// 3. 移动文件
	targetPath, err := moveFileToCategory(file.Path, classification.Category, organizer)
	if err != nil {
		log.Logger.Error("移动文件失败",
			zap.String("fileName", file.Name),
			zap.String("category", classification.Category),
			zap.Error(err))
		atomic.AddInt64(failureCount, 1)
		return
	}

	// 4. 更新索引
	indexManager.AddEntry(file, bookInfo, classification, targetPath)
	if err := indexManager.SaveIndex(); err != nil {
		log.Logger.Error("保存索引失败", zap.Error(err))
	}

	log.Logger.Info("文件处理完成",
		zap.String("fileName", file.Name),
		zap.String("category", classification.Category),
		zap.String("targetPath", targetPath))

	atomic.AddInt64(successCount, 1)
}

// moveFileToCategory 移动文件到指定分类目录
func moveFileToCategory(sourcePath, category string, organizer *ebook.FileOrganizer) (string, error) {
	return organizer.MoveFileToCategory(sourcePath, category)
}
