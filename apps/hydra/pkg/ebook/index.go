package ebook

import (
	"crypto/md5"
	"encoding/json"
	"fmt"
	"io"
	"math"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"sync"
	"time"

	"github.com/penwyp/hydra/shared/log"
	"go.uber.org/zap"
)

// 当前索引版本
const CurrentIndexVersion = "2.0"

// EnhancedIndexEntry 增强的索引条目
type EnhancedIndexEntry struct {
	// === 基本信息 ===
	FileName    string    `json:"file_name"`    // 原始文件名（不含扩展名）
	Category    string    `json:"category"`     // 分类结果
	ProcessedAt time.Time `json:"processed_at"` // 处理时间
	LastUpdated time.Time `json:"last_updated"` // 最后更新时间

	// === 书籍元数据（用于去重和验证） ===
	Title       string   `json:"title,omitempty"`        // 书名
	Author      string   `json:"author,omitempty"`       // 作者
	ISBN        string   `json:"isbn,omitempty"`         // ISBN号
	Publisher   string   `json:"publisher,omitempty"`    // 出版社
	PublishDate string   `json:"publish_date,omitempty"` // 出版日期
	Language    string   `json:"language,omitempty"`     // 语言
	Description string   `json:"description,omitempty"`  // 描述（截断版）
	Genre       []string `json:"genre,omitempty"`        // 类型/标签

	// === 文件特征（用于精确匹配） ===
	FileSize int64  `json:"file_size"`           // 文件大小
	FileExt  string `json:"file_ext"`            // 文件扩展名
	FileHash string `json:"file_hash,omitempty"` // 文件内容哈希（可选）

	// === 分类信息 ===
	Confidence float64 `json:"confidence"`       // 分类置信度
	Reason     string  `json:"reason,omitempty"` // 分类理由
	Source     string  `json:"source"`           // 元数据来源（file/douban/google/filename）

	// === 版本控制 ===
	Version      int    `json:"version"`       // 条目版本号
	IndexVersion string `json:"index_version"` // 索引格式版本
}

// EnhancedIndex 增强索引
type EnhancedIndex struct {
	// === 索引元信息 ===
	Version      string    `json:"version"`       // 索引版本
	CreatedAt    time.Time `json:"created_at"`    // 创建时间
	UpdatedAt    time.Time `json:"updated_at"`    // 更新时间
	TotalEntries int       `json:"total_entries"` // 总条目数

	// === 主索引：使用复合键 ===
	Entries map[string]*EnhancedIndexEntry `json:"entries"`

	// === 辅助索引（用于快速查找） ===
	// ISBN索引
	ISBNIndex map[string]string `json:"isbn_index,omitempty"` // ISBN -> 主键
	// 标题+作者索引
	TitleAuthorIndex map[string]string `json:"title_author_index,omitempty"` // 标题:作者 -> 主键
	// 文件名索引（支持多个同名文件）
	FileNameIndex map[string][]string `json:"filename_index,omitempty"` // 文件名 -> 主键列表
	// 哈希索引
	HashIndex map[string]string `json:"hash_index,omitempty"` // 文件哈希 -> 主键
}

// IndexManager 增强索引管理器
type IndexManager struct {
	indexPath    string
	index        *EnhancedIndex
	keyGenerator *IndexKeyGenerator
	mutex        sync.RWMutex

	// 配置选项
	enableFileHash    bool // 是否启用文件哈希
	maxDescriptionLen int  // 描述最大长度
}

// IndexKeyGenerator 索引键生成器
type IndexKeyGenerator struct{}

// FindResult 查找结果
type FindResult struct {
	Entry      *EnhancedIndexEntry   // 找到的条目
	MatchType  string                // 匹配类型：isbn/title_author/filename/hash
	Confidence float64               // 匹配置信度
	Conflicts  []*EnhancedIndexEntry // 冲突条目
}

// ConflictResolution 冲突解决策略
type ConflictResolution int

const (
	ConflictSkip      ConflictResolution = iota // 跳过处理
	ConflictOverwrite                           // 覆盖现有条目
	ConflictCreateNew                           // 创建新条目
	ConflictPrompt                              // 提示用户选择
)

// NewIndexManager 创建增强索引管理器
func NewIndexManager(targetDir string) *IndexManager {
	return &IndexManager{
		indexPath:         filepath.Join(targetDir, ".ebook_enhanced_index.json"),
		keyGenerator:      &IndexKeyGenerator{},
		enableFileHash:    false, // 默认关闭哈希计算（性能考虑）
		maxDescriptionLen: 200,   // 描述最大200字符
	}
}

// LoadIndex 加载索引文件
func (im *IndexManager) LoadIndex() error {
	// 如果索引文件不存在，创建新的索引
	if _, err := os.Stat(im.indexPath); os.IsNotExist(err) {
		im.index = im.createEmptyIndex()
		return im.SaveIndex()
	}

	// 读取现有索引文件
	data, err := os.ReadFile(im.indexPath)
	if err != nil {
		return fmt.Errorf("读取索引文件失败: %w", err)
	}

	im.index = &EnhancedIndex{}
	if err := json.Unmarshal(data, im.index); err != nil {
		log.Logger.Warn("索引文件格式错误，将创建新索引", zap.Error(err))
		im.index = im.createEmptyIndex()
		return im.SaveIndex()
	}

	// 验证索引版本
	if im.index.Version != CurrentIndexVersion {
		log.Logger.Warn("索引版本不匹配，将创建新索引",
			zap.String("currentVersion", im.index.Version),
			zap.String("expectedVersion", CurrentIndexVersion))
		im.index = im.createEmptyIndex()
		return im.SaveIndex()
	}

	// 初始化空的辅助索引（如果不存在）
	if im.index.ISBNIndex == nil {
		im.index.ISBNIndex = make(map[string]string)
	}
	if im.index.TitleAuthorIndex == nil {
		im.index.TitleAuthorIndex = make(map[string]string)
	}
	if im.index.FileNameIndex == nil {
		im.index.FileNameIndex = make(map[string][]string)
	}
	if im.index.HashIndex == nil {
		im.index.HashIndex = make(map[string]string)
	}

	log.Logger.Info("索引文件加载成功",
		zap.String("path", im.indexPath),
		zap.String("version", im.index.Version),
		zap.Int("entries", im.index.TotalEntries))

	return nil
}

// createEmptyIndex 创建空的索引
func (im *IndexManager) createEmptyIndex() *EnhancedIndex {
	now := time.Now()
	return &EnhancedIndex{
		Version:          CurrentIndexVersion,
		CreatedAt:        now,
		UpdatedAt:        now,
		TotalEntries:     0,
		Entries:          make(map[string]*EnhancedIndexEntry),
		ISBNIndex:        make(map[string]string),
		TitleAuthorIndex: make(map[string]string),
		FileNameIndex:    make(map[string][]string),
		HashIndex:        make(map[string]string),
	}
}

// SaveIndex 保存索引文件
func (im *IndexManager) SaveIndex() error {
	im.mutex.Lock()
	defer im.mutex.Unlock()

	if im.index == nil {
		return fmt.Errorf("索引为空，无法保存")
	}

	// 更新索引元信息
	im.index.UpdatedAt = time.Now()
	im.index.TotalEntries = len(im.index.Entries)

	data, err := json.MarshalIndent(im.index, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化索引失败: %w", err)
	}

	if err := os.WriteFile(im.indexPath, data, 0644); err != nil {
		return fmt.Errorf("保存索引文件失败: %w", err)
	}

	log.Logger.Debug("索引文件保存成功",
		zap.String("path", im.indexPath),
		zap.Int("entries", im.index.TotalEntries))
	return nil
}

// ===== 索引键生成器方法 =====

// GeneratePrimaryKey 生成主键
func (g *IndexKeyGenerator) GeneratePrimaryKey(file EbookFile, bookInfo *BookInfo) string {
	// 策略1: 如果有ISBN，使用ISBN作为主键
	if bookInfo != nil && bookInfo.ISBN != "" {
		return fmt.Sprintf("isbn:%s", normalizeISBN(bookInfo.ISBN))
	}

	// 策略2: 如果有完整的标题和作者，使用标题+作者
	if bookInfo != nil && bookInfo.Title != "" && bookInfo.Author != "" {
		titleKey := normalizeString(bookInfo.Title)
		authorKey := normalizeString(bookInfo.Author)
		return fmt.Sprintf("book:%s:%s", titleKey, authorKey)
	}

	// 策略3: 使用文件特征（文件名+大小+扩展名）
	return fmt.Sprintf("file:%s:%d:%s",
		normalizeString(file.Name),
		file.Size,
		strings.ToLower(file.Extension))
}

// GenerateSecondaryKeys 生成辅助索引键
func (g *IndexKeyGenerator) GenerateSecondaryKeys(file EbookFile, bookInfo *BookInfo) map[string]string {
	keys := make(map[string]string)

	if bookInfo != nil {
		// ISBN键
		if bookInfo.ISBN != "" {
			keys["isbn"] = normalizeISBN(bookInfo.ISBN)
		}

		// 标题+作者键
		if bookInfo.Title != "" && bookInfo.Author != "" {
			keys["title_author"] = fmt.Sprintf("%s:%s",
				normalizeString(bookInfo.Title),
				normalizeString(bookInfo.Author))
		}
	}

	// 文件名键
	keys["filename"] = normalizeString(file.Name)

	return keys
}

// ===== 字符串处理工具函数 =====

// normalizeString 字符串标准化函数
func normalizeString(s string) string {
	// 转小写
	s = strings.ToLower(s)
	// 移除特殊字符
	s = regexp.MustCompile(`[^\p{L}\p{N}\s]`).ReplaceAllString(s, "")
	// 标准化空格
	s = regexp.MustCompile(`\s+`).ReplaceAllString(s, " ")
	return strings.TrimSpace(s)
}

// normalizeISBN ISBN标准化
func normalizeISBN(isbn string) string {
	// 移除所有非数字字符
	return regexp.MustCompile(`[^\d]`).ReplaceAllString(isbn, "")
}

// ===== 查找方法 =====

// FindByFileName 根据文件名查找分类（兼容旧接口）
func (im *IndexManager) FindByFileName(fileName string) (string, bool) {
	entries := im.findByFileName(fileName)
	if len(entries) == 1 {
		return entries[0].Category, true
	}
	return "", false
}

// findByFileName 内部方法：根据文件名查找条目列表
func (im *IndexManager) findByFileName(fileName string) []*EnhancedIndexEntry {
	im.mutex.RLock()
	defer im.mutex.RUnlock()

	if im.index == nil {
		return nil
	}

	normalizedName := normalizeString(fileName)
	if primaryKeys, exists := im.index.FileNameIndex[normalizedName]; exists {
		entries := make([]*EnhancedIndexEntry, 0, len(primaryKeys))
		for _, key := range primaryKeys {
			if entry, exists := im.index.Entries[key]; exists {
				entries = append(entries, entry)
			}
		}
		return entries
	}
	return nil
}

// findByISBN 根据ISBN查找条目
func (im *IndexManager) findByISBN(isbn string) *EnhancedIndexEntry {
	im.mutex.RLock()
	defer im.mutex.RUnlock()

	if im.index == nil {
		return nil
	}

	normalizedISBN := normalizeISBN(isbn)
	if primaryKey, exists := im.index.ISBNIndex[normalizedISBN]; exists {
		return im.index.Entries[primaryKey]
	}
	return nil
}

// findByTitleAuthor 根据标题+作者查找条目
func (im *IndexManager) findByTitleAuthor(title, author string) *EnhancedIndexEntry {
	im.mutex.RLock()
	defer im.mutex.RUnlock()

	if im.index == nil {
		return nil
	}

	key := fmt.Sprintf("%s:%s", normalizeString(title), normalizeString(author))
	if primaryKey, exists := im.index.TitleAuthorIndex[key]; exists {
		return im.index.Entries[primaryKey]
	}
	return nil
}

// findByHash 根据文件哈希查找条目
func (im *IndexManager) findByHash(hash string) *EnhancedIndexEntry {
	im.mutex.RLock()
	defer im.mutex.RUnlock()

	if im.index == nil {
		return nil
	}

	if primaryKey, exists := im.index.HashIndex[hash]; exists {
		return im.index.Entries[primaryKey]
	}
	return nil
}

// FindByFilePath 根据文件路径查找分类
func (im *IndexManager) FindByFilePath(filePath string) (string, bool) {
	if im.index == nil {
		return "", false
	}

	fileName := filepath.Base(filePath)
	nameWithoutExt := fileName[:len(fileName)-len(filepath.Ext(fileName))]

	return im.FindByFileName(nameWithoutExt)
}

// AddEntry 添加索引条目
func (im *IndexManager) AddEntry(file EbookFile, bookInfo *BookInfo, classification *ClassificationResult, targetPath string) {
	im.mutex.Lock()
	defer im.mutex.Unlock()

	if im.index == nil {
		return
	}

	// 生成主键
	primaryKey := im.keyGenerator.GeneratePrimaryKey(file, bookInfo)

	// 创建索引条目
	now := time.Now()
	entry := &EnhancedIndexEntry{
		FileName:     file.Name,
		Category:     classification.Category,
		ProcessedAt:  now,
		LastUpdated:  now,
		FileSize:     file.Size,
		FileExt:      file.Extension,
		Confidence:   classification.Confidence,
		Reason:       classification.Reason,
		Version:      1,
		IndexVersion: CurrentIndexVersion,
	}

	// 填充书籍元数据
	if bookInfo != nil {
		entry.Title = bookInfo.Title
		entry.Author = bookInfo.Author
		entry.ISBN = bookInfo.ISBN
		entry.Publisher = bookInfo.Publisher
		entry.PublishDate = bookInfo.PublishDate
		entry.Language = bookInfo.Language
		entry.Genre = bookInfo.Genre

		// 截断描述
		if len(bookInfo.Description) > im.maxDescriptionLen {
			entry.Description = bookInfo.Description[:im.maxDescriptionLen] + "..."
		} else {
			entry.Description = bookInfo.Description
		}

		// 确定元数据来源
		if bookInfo.ISBN != "" {
			entry.Source = "api_with_isbn"
		} else if bookInfo.Title != "" && bookInfo.Author != "" {
			entry.Source = "api_with_metadata"
		} else {
			entry.Source = "filename"
		}
	} else {
		entry.Source = "filename"
	}

	// 计算文件哈希（如果启用）
	if im.enableFileHash {
		if hash := im.calculateFileHash(file.Path); hash != "" {
			entry.FileHash = hash
		}
	}

	// 添加到主索引
	im.index.Entries[primaryKey] = entry

	// 更新辅助索引
	im.updateSecondaryIndexes(primaryKey, entry, true)

	log.Logger.Debug("添加索引条目",
		zap.String("fileName", file.Name),
		zap.String("primaryKey", primaryKey),
		zap.String("category", classification.Category))
}

// updateSecondaryIndexes 更新辅助索引
func (im *IndexManager) updateSecondaryIndexes(primaryKey string, entry *EnhancedIndexEntry, isAdd bool) {
	// 更新ISBN索引
	if entry.ISBN != "" {
		normalizedISBN := normalizeISBN(entry.ISBN)
		if isAdd {
			im.index.ISBNIndex[normalizedISBN] = primaryKey
		} else {
			delete(im.index.ISBNIndex, normalizedISBN)
		}
	}

	// 更新标题+作者索引
	if entry.Title != "" && entry.Author != "" {
		key := fmt.Sprintf("%s:%s", normalizeString(entry.Title), normalizeString(entry.Author))
		if isAdd {
			im.index.TitleAuthorIndex[key] = primaryKey
		} else {
			delete(im.index.TitleAuthorIndex, key)
		}
	}

	// 更新文件名索引
	normalizedName := normalizeString(entry.FileName)
	if isAdd {
		im.index.FileNameIndex[normalizedName] = append(im.index.FileNameIndex[normalizedName], primaryKey)
	} else {
		// 从文件名索引中移除
		if keys, exists := im.index.FileNameIndex[normalizedName]; exists {
			newKeys := make([]string, 0, len(keys))
			for _, key := range keys {
				if key != primaryKey {
					newKeys = append(newKeys, key)
				}
			}
			if len(newKeys) > 0 {
				im.index.FileNameIndex[normalizedName] = newKeys
			} else {
				delete(im.index.FileNameIndex, normalizedName)
			}
		}
	}

	// 更新哈希索引
	if entry.FileHash != "" {
		if isAdd {
			im.index.HashIndex[entry.FileHash] = primaryKey
		} else {
			delete(im.index.HashIndex, entry.FileHash)
		}
	}
}

// calculateFileHash 计算文件哈希
func (im *IndexManager) calculateFileHash(filePath string) string {
	file, err := os.Open(filePath)
	if err != nil {
		log.Logger.Debug("无法打开文件计算哈希", zap.String("path", filePath), zap.Error(err))
		return ""
	}
	defer file.Close()

	hash := md5.New()
	if _, err := io.Copy(hash, file); err != nil {
		log.Logger.Debug("计算文件哈希失败", zap.String("path", filePath), zap.Error(err))
		return ""
	}

	return fmt.Sprintf("%x", hash.Sum(nil))
}

// UpdateEntry 更新索引条目（简化版，用于兼容）
func (im *IndexManager) UpdateEntry(fileName string, category string) {
	// 这个方法主要用于向后兼容，实际使用中应该使用 AddEntry
	log.Logger.Warn("使用了已废弃的 UpdateEntry 方法，建议使用 AddEntry",
		zap.String("fileName", fileName),
		zap.String("category", category))
}

// RemoveEntry 删除索引条目
func (im *IndexManager) RemoveEntry(fileName string) {
	im.mutex.Lock()
	defer im.mutex.Unlock()

	if im.index == nil {
		return
	}

	// 查找要删除的条目
	entries := im.findByFileNameUnsafe(fileName)
	for _, entry := range entries {
		// 找到对应的主键
		for primaryKey, indexEntry := range im.index.Entries {
			if indexEntry == entry {
				// 更新辅助索引
				im.updateSecondaryIndexes(primaryKey, entry, false)
				// 删除主条目
				delete(im.index.Entries, primaryKey)
				log.Logger.Debug("删除索引条目",
					zap.String("fileName", fileName),
					zap.String("primaryKey", primaryKey))
				break
			}
		}
	}
}

// findByFileNameUnsafe 内部方法：不加锁的文件名查找（用于已加锁的上下文）
func (im *IndexManager) findByFileNameUnsafe(fileName string) []*EnhancedIndexEntry {
	if im.index == nil {
		return nil
	}

	normalizedName := normalizeString(fileName)
	if primaryKeys, exists := im.index.FileNameIndex[normalizedName]; exists {
		entries := make([]*EnhancedIndexEntry, 0, len(primaryKeys))
		for _, key := range primaryKeys {
			if entry, exists := im.index.Entries[key]; exists {
				entries = append(entries, entry)
			}
		}
		return entries
	}
	return nil
}

// ExportIndex 导出索引到指定文件
func (im *IndexManager) ExportIndex(exportPath string) error {
	im.mutex.RLock()
	defer im.mutex.RUnlock()

	if im.index == nil {
		return fmt.Errorf("索引为空，无法导出")
	}

	data, err := json.MarshalIndent(im.index, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化索引失败: %w", err)
	}

	if err := os.WriteFile(exportPath, data, 0644); err != nil {
		return fmt.Errorf("导出索引文件失败: %w", err)
	}

	log.Logger.Info("索引导出成功",
		zap.String("path", exportPath),
		zap.Int("entries", im.index.TotalEntries))
	return nil
}

// PrintStats 打印索引统计信息
func (im *IndexManager) PrintStats() {
	im.mutex.RLock()
	defer im.mutex.RUnlock()

	if im.index == nil {
		fmt.Println("索引未加载")
		return
	}

	// 统计分类数量
	categoryStats := make(map[string]int)
	sourceStats := make(map[string]int)

	for _, entry := range im.index.Entries {
		categoryStats[entry.Category]++
		sourceStats[entry.Source]++
	}

	fmt.Printf("\n=== 电子书分类索引统计 ===\n")
	fmt.Printf("索引版本: %s\n", im.index.Version)
	fmt.Printf("创建时间: %s\n", im.index.CreatedAt.Format("2006-01-02 15:04:05"))
	fmt.Printf("更新时间: %s\n", im.index.UpdatedAt.Format("2006-01-02 15:04:05"))
	fmt.Printf("总条目数: %d\n", im.index.TotalEntries)

	if len(categoryStats) > 0 {
		fmt.Printf("\n分类统计:\n")
		for category, count := range categoryStats {
			fmt.Printf("  %s: %d 本\n", category, count)
		}
	}

	if len(sourceStats) > 0 {
		fmt.Printf("\n元数据来源统计:\n")
		for source, count := range sourceStats {
			fmt.Printf("  %s: %d 本\n", source, count)
		}
	}

	// 索引效率统计
	fmt.Printf("\n索引效率:\n")
	fmt.Printf("  ISBN索引: %d 条\n", len(im.index.ISBNIndex))
	fmt.Printf("  标题+作者索引: %d 条\n", len(im.index.TitleAuthorIndex))
	fmt.Printf("  文件名索引: %d 条\n", len(im.index.FileNameIndex))
	if im.enableFileHash {
		fmt.Printf("  文件哈希索引: %d 条\n", len(im.index.HashIndex))
	}
}

// ===== 智能查找功能 =====

// FindWithValidation 智能查找并验证
func (im *IndexManager) FindWithValidation(file EbookFile, bookInfo *BookInfo) *FindResult {
	im.mutex.RLock()
	defer im.mutex.RUnlock()

	result := &FindResult{
		Conflicts: make([]*EnhancedIndexEntry, 0),
	}

	// === 查找策略1: ISBN精确匹配（最高优先级） ===
	if bookInfo != nil && bookInfo.ISBN != "" {
		if entry := im.findByISBNUnsafe(bookInfo.ISBN); entry != nil {
			result.Entry = entry
			result.MatchType = "isbn"
			result.Confidence = 1.0
			return result
		}
	}

	// === 查找策略2: 标题+作者匹配 ===
	if bookInfo != nil && bookInfo.Title != "" && bookInfo.Author != "" {
		if entry := im.findByTitleAuthorUnsafe(bookInfo.Title, bookInfo.Author); entry != nil {
			// 验证文件特征是否匹配
			if im.validateFileMatch(entry, file) {
				result.Entry = entry
				result.MatchType = "title_author"
				result.Confidence = 0.9
				return result
			} else {
				// 标题作者匹配但文件特征不匹配，可能是不同版本
				result.Conflicts = append(result.Conflicts, entry)
			}
		}
	}

	// === 查找策略3: 文件哈希匹配（如果启用） ===
	if im.enableFileHash {
		if hash := im.calculateFileHash(file.Path); hash != "" {
			if entry := im.findByHashUnsafe(hash); entry != nil {
				result.Entry = entry
				result.MatchType = "hash"
				result.Confidence = 1.0
				return result
			}
		}
	}

	// === 查找策略4: 文件名匹配（需要验证） ===
	entries := im.findByFileNameUnsafe(file.Name)
	if len(entries) > 0 {
		// 如果只有一个匹配项
		if len(entries) == 1 {
			entry := entries[0]
			confidence := im.calculateSimilarity(entry, bookInfo, file)
			if confidence > 0.7 { // 相似度阈值
				result.Entry = entry
				result.MatchType = "filename"
				result.Confidence = confidence
				return result
			}
		}

		// 多个匹配项，都作为冲突处理
		result.Conflicts = entries
	}

	return result
}

// 不加锁的查找方法（用于已加锁的上下文）
func (im *IndexManager) findByISBNUnsafe(isbn string) *EnhancedIndexEntry {
	if im.index == nil {
		return nil
	}

	normalizedISBN := normalizeISBN(isbn)
	if primaryKey, exists := im.index.ISBNIndex[normalizedISBN]; exists {
		return im.index.Entries[primaryKey]
	}
	return nil
}

func (im *IndexManager) findByTitleAuthorUnsafe(title, author string) *EnhancedIndexEntry {
	if im.index == nil {
		return nil
	}

	key := fmt.Sprintf("%s:%s", normalizeString(title), normalizeString(author))
	if primaryKey, exists := im.index.TitleAuthorIndex[key]; exists {
		return im.index.Entries[primaryKey]
	}
	return nil
}

func (im *IndexManager) findByHashUnsafe(hash string) *EnhancedIndexEntry {
	if im.index == nil {
		return nil
	}

	if primaryKey, exists := im.index.HashIndex[hash]; exists {
		return im.index.Entries[primaryKey]
	}
	return nil
}

// ===== 验证和相似度计算 =====

// validateFileMatch 验证文件特征是否匹配
func (im *IndexManager) validateFileMatch(entry *EnhancedIndexEntry, file EbookFile) bool {
	// 文件大小差异不能太大（允许10%的差异）
	if entry.FileSize > 0 && file.Size > 0 {
		sizeDiff := math.Abs(float64(entry.FileSize - file.Size))
		maxSize := math.Max(float64(entry.FileSize), float64(file.Size))
		if sizeDiff/maxSize > 0.1 { // 超过10%差异
			return false
		}
	}

	// 文件扩展名必须匹配
	if entry.FileExt != "" && file.Extension != "" {
		if strings.ToLower(entry.FileExt) != strings.ToLower(file.Extension) {
			return false
		}
	}

	return true
}

// calculateSimilarity 计算条目与新书的相似度
func (im *IndexManager) calculateSimilarity(entry *EnhancedIndexEntry, bookInfo *BookInfo, file EbookFile) float64 {
	score := 0.0
	factors := 0

	if bookInfo != nil {
		// 标题相似度（权重：0.4）
		if entry.Title != "" && bookInfo.Title != "" {
			titleSim := im.stringSimilarity(entry.Title, bookInfo.Title)
			score += titleSim * 0.4
			factors++
		}

		// 作者相似度（权重：0.3）
		if entry.Author != "" && bookInfo.Author != "" {
			authorSim := im.stringSimilarity(entry.Author, bookInfo.Author)
			score += authorSim * 0.3
			factors++
		}

		// 出版社相似度（权重：0.1）
		if entry.Publisher != "" && bookInfo.Publisher != "" {
			pubSim := im.stringSimilarity(entry.Publisher, bookInfo.Publisher)
			score += pubSim * 0.1
			factors++
		}
	}

	// 文件大小相似度（权重：0.2）
	if entry.FileSize > 0 && file.Size > 0 {
		sizeDiff := math.Abs(float64(entry.FileSize - file.Size))
		maxSize := math.Max(float64(entry.FileSize), float64(file.Size))
		sizeSim := 1.0 - (sizeDiff / maxSize)
		score += sizeSim * 0.2
		factors++
	}

	if factors == 0 {
		return 0.0
	}

	return score / float64(factors)
}

// stringSimilarity 计算字符串相似度（简化版）
func (im *IndexManager) stringSimilarity(s1, s2 string) float64 {
	s1 = normalizeString(s1)
	s2 = normalizeString(s2)

	if s1 == s2 {
		return 1.0
	}

	// 简化实现：基于公共子串
	longer, shorter := s1, s2
	if len(s1) < len(s2) {
		longer, shorter = s2, s1
	}

	if len(longer) == 0 {
		return 0.0
	}

	// 计算最长公共子序列长度
	lcs := im.longestCommonSubsequence(longer, shorter)
	return float64(lcs) / float64(len(longer))
}

// longestCommonSubsequence 计算最长公共子序列长度
func (im *IndexManager) longestCommonSubsequence(s1, s2 string) int {
	m, n := len(s1), len(s2)
	if m == 0 || n == 0 {
		return 0
	}

	// 动态规划表
	dp := make([][]int, m+1)
	for i := range dp {
		dp[i] = make([]int, n+1)
	}

	// 填充DP表
	for i := 1; i <= m; i++ {
		for j := 1; j <= n; j++ {
			if s1[i-1] == s2[j-1] {
				dp[i][j] = dp[i-1][j-1] + 1
			} else {
				dp[i][j] = int(math.Max(float64(dp[i-1][j]), float64(dp[i][j-1])))
			}
		}
	}

	return dp[m][n]
}

// GetIndex 获取索引数据（用于重建索引时的数据传递）
func (im *IndexManager) GetIndex() *EnhancedIndex {
	im.mutex.RLock()
	defer im.mutex.RUnlock()
	return im.index
}

// ReplaceIndex 替换索引数据（用于重建索引时更新数据）
func (im *IndexManager) ReplaceIndex(newIndex *EnhancedIndex) {
	im.mutex.Lock()
	defer im.mutex.Unlock()
	im.index = newIndex
}
