package ebook

import (
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/penwyp/hydra/shared/log"
	"go.uber.org/zap"
)

// ErrFileExists 文件已存在错误
var ErrFileExists = errors.New("目标文件已存在")

// OrganizeOptions 整理选项
type OrganizeOptions struct {
	SourceDir   string // 源目录
	TargetDir   string // 目标目录
	DefaultMove bool   // 分类失败时是否移动到默认目录
	DryRun      bool   // 是否为试运行模式
}

// OrganizeResult 整理结果
type OrganizeResult struct {
	TotalFiles     int               // 总文件数
	SuccessCount   int               // 成功处理数
	FailureCount   int               // 失败处理数
	SkippedCount   int               // 跳过处理数
	ErrorLog       []string          // 错误日志
	CategoryStats  map[string]int    // 各分类统计
	ProcessedFiles map[string]string // 已处理文件映射 (源路径 -> 目标路径)
}

// FileOrganizer 文件整理器
type FileOrganizer struct {
	options OrganizeOptions
}

// NewFileOrganizer 创建新的文件整理器
func NewFileOrganizer(options OrganizeOptions) *FileOrganizer {
	return &FileOrganizer{
		options: options,
	}
}

// sanitizeFileName 清理文件名，移除不安全字符
func sanitizeFileName(filename string) string {
	// 替换不安全的字符
	unsafe := []string{"/", "\\", ":", "*", "?", "\"", "<", ">", "|"}
	safe := filename

	for _, char := range unsafe {
		safe = strings.ReplaceAll(safe, char, "_")
	}

	// 移除多余的空格和点
	safe = strings.TrimSpace(safe)
	safe = strings.Trim(safe, ".")

	// 确保文件名不为空
	if safe == "" {
		safe = "unnamed"
	}

	return safe
}

// MoveFileToCategory 移动单个文件到指定分类目录（公开方法）
func (o *FileOrganizer) MoveFileToCategory(sourcePath, category string) (string, error) {
	return o.moveFile(sourcePath, category)
}

// createCategoryDir 创建分类目录
func (o *FileOrganizer) createCategoryDir(category string) (string, error) {
	categoryDir := filepath.Join(o.options.TargetDir, sanitizeFileName(category))

	if o.options.DryRun {
		log.Logger.Info("试运行模式：将创建目录", zap.String("dir", categoryDir))
		return categoryDir, nil
	}

	if err := os.MkdirAll(categoryDir, 0755); err != nil {
		return "", fmt.Errorf("创建分类目录失败: %w", err)
	}

	return categoryDir, nil
}

// checkFileExists 检查文件是否已存在
func (o *FileOrganizer) checkFileExists(targetDir, originalName string) (string, bool) {
	targetPath := filepath.Join(targetDir, originalName)
	_, err := os.Stat(targetPath)
	return targetPath, !os.IsNotExist(err)
}

// moveFile 移动文件
func (o *FileOrganizer) moveFile(sourcePath, category string) (string, error) {
	// 创建分类目录
	categoryDir, err := o.createCategoryDir(category)
	if err != nil {
		return "", err
	}

	// 获取原始文件名
	originalFileName := filepath.Base(sourcePath)

	// 检查文件是否已存在
	targetPath, exists := o.checkFileExists(categoryDir, originalFileName)
	if exists {
		return "", fmt.Errorf("%w: %s", ErrFileExists, targetPath)
	}

	if o.options.DryRun {
		log.Logger.Info("试运行模式：将移动文件",
			zap.String("from", sourcePath),
			zap.String("to", targetPath),
			zap.String("category", category))
		return targetPath, nil
	}

	// 执行文件移动
	if err := os.Rename(sourcePath, targetPath); err != nil {
		// 如果重命名失败（可能跨文件系统），尝试复制后删除
		if err := o.copyAndRemove(sourcePath, targetPath); err != nil {
			return "", fmt.Errorf("移动文件失败: %w", err)
		}
	}

	log.Logger.Info("文件移动成功",
		zap.String("from", sourcePath),
		zap.String("to", targetPath),
		zap.String("category", category))

	return targetPath, nil
}

// copyAndRemove 复制文件后删除原文件
func (o *FileOrganizer) copyAndRemove(sourcePath, targetPath string) error {
	// 读取源文件
	sourceData, err := os.ReadFile(sourcePath)
	if err != nil {
		return fmt.Errorf("读取源文件失败: %w", err)
	}

	// 写入目标文件
	if err := os.WriteFile(targetPath, sourceData, 0644); err != nil {
		return fmt.Errorf("写入目标文件失败: %w", err)
	}

	// 删除源文件
	if err := os.Remove(sourcePath); err != nil {
		// 如果删除失败，尝试删除已创建的目标文件
		os.Remove(targetPath)
		return fmt.Errorf("删除源文件失败: %w", err)
	}

	return nil
}

// OrganizeFiles 整理文件
func (o *FileOrganizer) OrganizeFiles(
	files []EbookFile,
	bookInfos map[string]*BookInfo,
	classifications map[string]*ClassificationResult,
) *OrganizeResult {

	result := &OrganizeResult{
		TotalFiles:     len(files),
		CategoryStats:  make(map[string]int),
		ProcessedFiles: make(map[string]string),
		ErrorLog:       make([]string, 0),
	}

	log.Logger.Info("开始整理文件",
		zap.Int("totalFiles", result.TotalFiles),
		zap.String("targetDir", o.options.TargetDir),
		zap.Bool("dryRun", o.options.DryRun))

	for i, file := range files {
		log.Logger.Info("处理文件",
			zap.Int("current", i+1),
			zap.Int("total", result.TotalFiles),
			zap.String("file", file.Name))

		// 获取书本信息
		bookInfo, hasBookInfo := bookInfos[file.Path]
		if !hasBookInfo {
			errorMsg := fmt.Sprintf("文件 %s 缺少书本信息", file.Path)
			result.ErrorLog = append(result.ErrorLog, errorMsg)
			result.SkippedCount++
			log.Logger.Warn("跳过文件：缺少书本信息", zap.String("path", file.Path))
			continue
		}

		// 获取分类结果
		classification, hasClassification := classifications[file.Path]
		if !hasClassification {
			errorMsg := fmt.Sprintf("文件 %s 缺少分类信息，跳过处理", file.Path)
			result.ErrorLog = append(result.ErrorLog, errorMsg)
			result.SkippedCount++
			log.Logger.Warn("跳过文件：缺少分类信息", zap.String("path", file.Path))
			continue
		}

		// 移动文件
		targetPath, err := o.moveFile(file.Path, classification.Category)
		if err != nil {
			// 检查是否为文件已存在错误
			if errors.Is(err, ErrFileExists) {
				// 文件已存在，记录warn日志并跳过
				log.Logger.Warn("跳过文件：目标位置已存在同名文件",
					zap.String("path", file.Path),
					zap.String("category", classification.Category),
					zap.String("title", bookInfo.Title))
				result.SkippedCount++
				continue
			}

			// 其他错误，按原逻辑处理
			errorMsg := fmt.Sprintf("移动文件 %s 失败: %v", file.Path, err)
			result.ErrorLog = append(result.ErrorLog, errorMsg)
			result.FailureCount++
			log.Logger.Error("移动文件失败",
				zap.String("path", file.Path),
				zap.String("category", classification.Category),
				zap.Error(err))
			continue
		}

		// 记录成功处理
		result.SuccessCount++
		result.ProcessedFiles[file.Path] = targetPath
		result.CategoryStats[classification.Category]++

		log.Logger.Info("文件处理成功",
			zap.String("title", bookInfo.Title),
			zap.String("category", classification.Category),
			zap.Float64("confidence", classification.Confidence))
	}

	// 输出整理结果统计
	o.logOrganizeResults(result)

	return result
}

// logOrganizeResults 记录整理结果
func (o *FileOrganizer) logOrganizeResults(result *OrganizeResult) {
	log.Logger.Info("文件整理完成",
		zap.Int("totalFiles", result.TotalFiles),
		zap.Int("successCount", result.SuccessCount),
		zap.Int("failureCount", result.FailureCount),
		zap.Int("skippedCount", result.SkippedCount))

	// 输出分类统计
	if len(result.CategoryStats) > 0 {
		log.Logger.Info("分类统计:")
		for category, count := range result.CategoryStats {
			log.Logger.Info("",
				zap.String("category", category),
				zap.Int("count", count))
		}
	}

	// 输出错误日志
	if len(result.ErrorLog) > 0 {
		log.Logger.Warn("处理过程中的错误:")
		for _, errorMsg := range result.ErrorLog {
			log.Logger.Warn("", zap.String("error", errorMsg))
		}
	}
}

// SaveErrorLog 保存错误日志到文件
func (o *FileOrganizer) SaveErrorLog(result *OrganizeResult) error {
	if len(result.ErrorLog) == 0 {
		return nil
	}

	timestamp := time.Now().Format("20060102_150405")
	logFileName := fmt.Sprintf("ebook_classify_errors_%s.log", timestamp)
	logFilePath := filepath.Join(o.options.TargetDir, logFileName)

	var logContent strings.Builder
	logContent.WriteString(fmt.Sprintf("电子书分类错误日志 - %s\n", time.Now().Format("2006-01-02 15:04:05")))
	logContent.WriteString(fmt.Sprintf("总文件数: %d\n", result.TotalFiles))
	logContent.WriteString(fmt.Sprintf("成功处理: %d\n", result.SuccessCount))
	logContent.WriteString(fmt.Sprintf("处理失败: %d\n", result.FailureCount))
	logContent.WriteString(fmt.Sprintf("跳过处理: %d\n", result.SkippedCount))
	logContent.WriteString("\n错误详情:\n")

	for i, errorMsg := range result.ErrorLog {
		logContent.WriteString(fmt.Sprintf("%d. %s\n", i+1, errorMsg))
	}

	if err := os.WriteFile(logFilePath, []byte(logContent.String()), 0644); err != nil {
		return fmt.Errorf("保存错误日志失败: %w", err)
	}

	log.Logger.Info("错误日志已保存", zap.String("path", logFilePath))
	return nil
}
