package ebook

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"path/filepath"
	"regexp"
	"strings"
	"time"

	"github.com/gen2brain/go-fitz"
	"github.com/penwyp/hydra/shared/log"
	"github.com/pirmd/epub"
	"go.uber.org/zap"
)

// BookInfo 书本信息结构
type BookInfo struct {
	Title       string   `json:"title"`
	Author      string   `json:"author"`
	Publisher   string   `json:"publisher"`
	PublishDate string   `json:"publish_date"`
	ISBN        string   `json:"isbn"`
	Description string   `json:"description"`
	Genre       []string `json:"genre"`
	Language    string   `json:"language"`
	PageCount   int      `json:"page_count"`
	Rating      float64  `json:"rating"`
}

// MetadataRetriever 元数据检索器
type MetadataRetriever struct {
	httpClient *http.Client
	apiKeys    map[string]string // 各种API的密钥
}

// NewMetadataRetriever 创建新的元数据检索器
func NewMetadataRetriever() *MetadataRetriever {
	return &MetadataRetriever{
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		apiKeys: make(map[string]string),
	}
}

// SetAPIKey 设置API密钥
func (m *MetadataRetriever) SetAPIKey(service, key string) {
	m.apiKeys[service] = key
}

// cleanFileName 清理文件名，提取可能的书名和作者信息
func (m *MetadataRetriever) cleanFileName(filename string) (title, author string) {
	// 移除常见的文件名模式
	cleaned := filename

	// 移除括号内容（通常是版本、格式等信息）
	re := regexp.MustCompile(`\([^)]*\)|\[[^\]]*\]|\{[^}]*\}`)
	cleaned = re.ReplaceAllString(cleaned, "")

	// 移除常见的分隔符和多余空格
	cleaned = strings.ReplaceAll(cleaned, "_", " ")
	cleaned = strings.ReplaceAll(cleaned, "-", " ")
	cleaned = regexp.MustCompile(`\s+`).ReplaceAllString(cleaned, " ")
	cleaned = strings.TrimSpace(cleaned)

	// 尝试分离作者和书名（常见模式：作者 - 书名 或 书名 - 作者）
	if strings.Contains(cleaned, " - ") {
		parts := strings.Split(cleaned, " - ")
		if len(parts) == 2 {
			// 简单启发式：较短的通常是作者名
			if len(parts[0]) < len(parts[1]) {
				author = strings.TrimSpace(parts[0])
				title = strings.TrimSpace(parts[1])
			} else {
				title = strings.TrimSpace(parts[0])
				author = strings.TrimSpace(parts[1])
			}
		}
	}

	// 如果没有找到分隔符，整个作为书名
	if title == "" {
		title = cleaned
	}

	return title, author
}

// searchDouban 使用豆瓣搜索书本信息
func (m *MetadataRetriever) searchDouban(query string) (*BookInfo, error) {
	// 豆瓣读书搜索API（注意：豆瓣API可能需要特殊处理反爬虫）
	baseURL := "https://book.douban.com/j/subject_suggest"
	params := url.Values{}
	params.Add("q", query)

	fullURL := fmt.Sprintf("%s?%s", baseURL, params.Encode())

	// 设置User-Agent模拟浏览器请求
	req, err := http.NewRequest("GET", fullURL, nil)
	if err != nil {
		return nil, fmt.Errorf("创建豆瓣请求失败: %w", err)
	}

	req.Header.Set("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
	req.Header.Set("Referer", "https://book.douban.com/")

	resp, err := m.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("豆瓣API请求失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("豆瓣API返回错误状态: %d", resp.StatusCode)
	}

	var result []struct {
		Title  string `json:"title"`
		Author string `json:"author_name"`
		Pic    string `json:"pic"`
		URL    string `json:"url"`
		Year   string `json:"year"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, fmt.Errorf("解析豆瓣API响应失败: %w", err)
	}

	if len(result) == 0 {
		return nil, fmt.Errorf("豆瓣未找到匹配的书本信息")
	}

	item := result[0]
	bookInfo := &BookInfo{
		Title:       item.Title,
		Author:      item.Author,
		PublishDate: item.Year,
		Language:    "zh", // 豆瓣主要是中文书籍
	}

	return bookInfo, nil
}

// searchGoogleBooks 使用Google Books API搜索书本信息
func (m *MetadataRetriever) searchGoogleBooks(query string) (*BookInfo, error) {
	baseURL := "https://www.googleapis.com/books/v1/volumes"
	params := url.Values{}
	params.Add("q", query)
	params.Add("maxResults", "1")

	if apiKey, exists := m.apiKeys["google"]; exists {
		params.Add("key", apiKey)
	}

	fullURL := fmt.Sprintf("%s?%s", baseURL, params.Encode())

	resp, err := m.httpClient.Get(fullURL)
	if err != nil {
		return nil, fmt.Errorf("Google Books API请求失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("Google Books API返回错误状态: %d", resp.StatusCode)
	}

	var result struct {
		Items []struct {
			VolumeInfo struct {
				Title               string   `json:"title"`
				Authors             []string `json:"authors"`
				Publisher           string   `json:"publisher"`
				PublishedDate       string   `json:"publishedDate"`
				Description         string   `json:"description"`
				Categories          []string `json:"categories"`
				Language            string   `json:"language"`
				PageCount           int      `json:"pageCount"`
				AverageRating       float64  `json:"averageRating"`
				IndustryIdentifiers []struct {
					Type       string `json:"type"`
					Identifier string `json:"identifier"`
				} `json:"industryIdentifiers"`
			} `json:"volumeInfo"`
		} `json:"items"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, fmt.Errorf("解析Google Books API响应失败: %w", err)
	}

	if len(result.Items) == 0 {
		return nil, fmt.Errorf("未找到匹配的书本信息")
	}

	item := result.Items[0].VolumeInfo
	bookInfo := &BookInfo{
		Title:       item.Title,
		Publisher:   item.Publisher,
		PublishDate: item.PublishedDate,
		Description: item.Description,
		Language:    item.Language,
		PageCount:   item.PageCount,
		Rating:      item.AverageRating,
	}

	// 处理作者
	if len(item.Authors) > 0 {
		bookInfo.Author = strings.Join(item.Authors, ", ")
	}

	// 处理分类/类型
	if len(item.Categories) > 0 {
		bookInfo.Genre = item.Categories
	}

	// 处理ISBN
	for _, identifier := range item.IndustryIdentifiers {
		if identifier.Type == "ISBN_13" || identifier.Type == "ISBN_10" {
			bookInfo.ISBN = identifier.Identifier
			break
		}
	}

	return bookInfo, nil
}

// ExtractFileMetadata 直接从文件中提取元数据
func (m *MetadataRetriever) ExtractFileMetadata(filePath string) (*BookInfo, error) {
	ext := strings.ToLower(filepath.Ext(filePath))

	switch ext {
	case ".epub":
		return m.extractEpubMetadata(filePath)
	case ".pdf":
		return m.extractPdfMetadata(filePath)
	case ".mobi", ".azw", ".azw3":
		// MOBI/AZW 格式比较复杂，暂时不支持直接提取
		return nil, fmt.Errorf("暂不支持 %s 格式的元数据提取", ext)
	default:
		return nil, fmt.Errorf("不支持的文件格式: %s", ext)
	}
}

// extractEpubMetadata 从 EPUB 文件中提取元数据
func (m *MetadataRetriever) extractEpubMetadata(filePath string) (*BookInfo, error) {
	// 使用 GetMetadataFromFile 函数直接获取元数据
	info, err := epub.GetMetadataFromFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("提取 EPUB 元数据失败: %w", err)
	}

	bookInfo := &BookInfo{}

	// 提取标题
	if len(info.Title) > 0 {
		bookInfo.Title = info.Title[0]
	}

	// 提取作者
	if len(info.Creator) > 0 {
		var authorNames []string
		for _, author := range info.Creator {
			if author.FullName != "" {
				authorNames = append(authorNames, author.FullName)
			}
		}
		if len(authorNames) > 0 {
			bookInfo.Author = strings.Join(authorNames, ", ")
		}
	}

	// 提取出版商
	if len(info.Publisher) > 0 {
		bookInfo.Publisher = info.Publisher[0]
	}

	// 提取语言
	if len(info.Language) > 0 {
		bookInfo.Language = info.Language[0]
	}

	// 提取描述
	if len(info.Description) > 0 {
		bookInfo.Description = info.Description[0]
	}

	// 提取日期
	if len(info.Date) > 0 {
		bookInfo.PublishDate = info.Date[0].Stamp
	}

	// 提取 ISBN
	if len(info.Identifier) > 0 {
		for _, id := range info.Identifier {
			if strings.Contains(strings.ToLower(id.Scheme), "isbn") {
				bookInfo.ISBN = id.Value
				break
			}
		}
	}

	// 提取主题/类型
	if len(info.Subject) > 0 {
		bookInfo.Genre = info.Subject
	}

	return bookInfo, nil
}

// extractPdfMetadata 从 PDF 文件中提取元数据
func (m *MetadataRetriever) extractPdfMetadata(filePath string) (*BookInfo, error) {
	doc, err := fitz.New(filePath)
	if err != nil {
		return nil, fmt.Errorf("打开 PDF 文件失败: %w", err)
	}
	defer doc.Close()

	metadata := doc.Metadata()
	bookInfo := &BookInfo{}

	// 提取标题
	if title, ok := metadata["title"]; ok && title != "" {
		bookInfo.Title = title
	}

	// 提取作者
	if author, ok := metadata["author"]; ok && author != "" {
		bookInfo.Author = author
	}

	// 提取创建日期作为出版日期
	if creationDate, ok := metadata["creationDate"]; ok && creationDate != "" {
		bookInfo.PublishDate = creationDate
	}

	// 提取主题作为描述
	if subject, ok := metadata["subject"]; ok && subject != "" {
		bookInfo.Description = subject
	}

	// 提取关键词作为类型
	if keywords, ok := metadata["keywords"]; ok && keywords != "" {
		// 将关键词按逗号分割作为类型
		bookInfo.Genre = strings.Split(keywords, ",")
		for i, genre := range bookInfo.Genre {
			bookInfo.Genre[i] = strings.TrimSpace(genre)
		}
	}

	return bookInfo, nil
}

// RetrieveMetadata 检索书本元数据（增强版）
func (m *MetadataRetriever) RetrieveMetadata(filename string) (*BookInfo, error) {
	return m.RetrieveMetadataWithPath(filename, "")
}

// RetrieveMetadataWithPath 检索书本元数据，支持文件路径
func (m *MetadataRetriever) RetrieveMetadataWithPath(filename, filePath string) (*BookInfo, error) {
	var bookInfo *BookInfo
	var fileMetadataErr error

	// 1. 首先尝试从文件中直接提取元数据（如果提供了文件路径）
	if filePath != "" {
		bookInfo, fileMetadataErr = m.ExtractFileMetadata(filePath)
		if fileMetadataErr == nil && bookInfo != nil {
			log.Logger.Info("成功从文件中提取元数据",
				zap.String("filename", filename),
				zap.String("title", bookInfo.Title),
				zap.String("author", bookInfo.Author))

			// 如果文件元数据提取成功且信息比较完整，直接返回
			if bookInfo.Title != "" && bookInfo.Author != "" {
				return bookInfo, nil
			}
		} else {
			log.Logger.Debug("文件元数据提取失败，将继续使用文件名和API搜索",
				zap.String("filename", filename),
				zap.Error(fileMetadataErr))
		}
	}

	// 2. 从文件名提取信息
	title, author := m.cleanFileName(filename)

	log.Logger.Debug("提取文件名信息",
		zap.String("filename", filename),
		zap.String("title", title),
		zap.String("author", author))

	// 如果从文件中提取到了部分信息，与文件名信息合并
	if bookInfo != nil {
		if bookInfo.Title == "" && title != "" {
			bookInfo.Title = title
		}
		if bookInfo.Author == "" && author != "" {
			bookInfo.Author = author
		}
		// 如果现在信息比较完整，可以直接返回
		if bookInfo.Title != "" && bookInfo.Author != "" {
			return bookInfo, nil
		}
	}

	// 3. 构建搜索查询
	var query string
	searchTitle := title
	searchAuthor := author

	// 优先使用文件元数据中的信息
	if bookInfo != nil {
		if bookInfo.Title != "" {
			searchTitle = bookInfo.Title
		}
		if bookInfo.Author != "" {
			searchAuthor = bookInfo.Author
		}
	}

	if searchAuthor != "" {
		query = fmt.Sprintf("%s %s", searchTitle, searchAuthor)
	} else {
		query = searchTitle
	}

	// 4. 优先尝试豆瓣搜索
	apiBookInfo, err := m.searchDouban(query)
	if err != nil {
		log.Logger.Debug("豆瓣搜索失败，尝试Google Books",
			zap.String("query", query),
			zap.Error(err))

		// 尝试使用Google Books API
		apiBookInfo, err = m.searchGoogleBooks(query)
		if err != nil {
			log.Logger.Warn("Google Books API搜索也失败",
				zap.String("query", query),
				zap.Error(err))

			// 如果API搜索都失败了，返回已有的信息（文件元数据 + 文件名信息）
			if bookInfo != nil {
				return bookInfo, nil
			}

			// 返回基于文件名的基本信息
			return &BookInfo{
				Title:  title,
				Author: author,
			}, nil
		}
	}

	// 5. 合并文件元数据和API搜索结果
	if bookInfo != nil {
		// 用API结果补充文件元数据中缺失的信息
		if bookInfo.Title == "" {
			bookInfo.Title = apiBookInfo.Title
		}
		if bookInfo.Author == "" {
			bookInfo.Author = apiBookInfo.Author
		}
		if bookInfo.Publisher == "" {
			bookInfo.Publisher = apiBookInfo.Publisher
		}
		if bookInfo.PublishDate == "" {
			bookInfo.PublishDate = apiBookInfo.PublishDate
		}
		if bookInfo.ISBN == "" {
			bookInfo.ISBN = apiBookInfo.ISBN
		}
		if bookInfo.Description == "" {
			bookInfo.Description = apiBookInfo.Description
		}
		if len(bookInfo.Genre) == 0 {
			bookInfo.Genre = apiBookInfo.Genre
		}
		if bookInfo.Language == "" {
			bookInfo.Language = apiBookInfo.Language
		}
		if bookInfo.PageCount == 0 {
			bookInfo.PageCount = apiBookInfo.PageCount
		}
		if bookInfo.Rating == 0 {
			bookInfo.Rating = apiBookInfo.Rating
		}
	} else {
		bookInfo = apiBookInfo
	}

	log.Logger.Info("成功检索书本信息",
		zap.String("filename", filename),
		zap.String("title", bookInfo.Title),
		zap.String("author", bookInfo.Author),
		zap.Bool("hasFileMetadata", fileMetadataErr == nil))

	return bookInfo, nil
}

// BatchRetrieveMetadata 批量检索元数据
func (m *MetadataRetriever) BatchRetrieveMetadata(files []EbookFile) map[string]*BookInfo {
	result := make(map[string]*BookInfo)

	for i, file := range files {
		log.Logger.Info("检索书本信息",
			zap.Int("current", i+1),
			zap.Int("total", len(files)),
			zap.String("filename", file.Name))

		bookInfo, err := m.RetrieveMetadata(file.Name)
		if err != nil {
			log.Logger.Error("检索书本信息失败",
				zap.String("filename", file.Name),
				zap.Error(err))
			continue
		}

		result[file.Path] = bookInfo

		// 添加延迟避免API限制
		if i < len(files)-1 {
			time.Sleep(100 * time.Millisecond)
		}
	}

	return result
}
