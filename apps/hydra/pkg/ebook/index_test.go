package ebook

import (
	"os"
	"path/filepath"
	"testing"
	"time"
)

func TestEnhancedIndexManager(t *testing.T) {
	// 创建临时目录
	tempDir, err := os.MkdirTemp("", "ebook_index_test")
	if err != nil {
		t.Fatalf("创建临时目录失败: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 创建索引管理器
	indexManager := NewIndexManager(tempDir)
	
	// 加载索引
	if err := indexManager.LoadIndex(); err != nil {
		t.Fatalf("加载索引失败: %v", err)
	}

	// 测试数据
	file1 := EbookFile{
		Name:      "Go语言编程",
		Path:      "/test/Go语言编程.pdf",
		Size:      1024000,
		Extension: ".pdf",
	}
	
	bookInfo1 := &BookInfo{
		Title:       "Go语言编程",
		Author:      "张三",
		ISBN:        "9787111234567",
		Publisher:   "机械工业出版社",
		PublishDate: "2023-01-01",
		Language:    "zh",
		Description: "这是一本关于Go语言编程的书籍",
		Genre:       []string{"编程", "计算机"},
	}
	
	classification1 := &ClassificationResult{
		Category:   "应用学科·技术与编程",
		Confidence: 0.95,
		Reason:     "基于书名和内容分析",
	}

	// 测试添加条目
	indexManager.AddEntry(file1, bookInfo1, classification1, "/target/path")
	
	// 测试保存索引
	if err := indexManager.SaveIndex(); err != nil {
		t.Fatalf("保存索引失败: %v", err)
	}

	// 测试查找功能
	t.Run("TestFindByISBN", func(t *testing.T) {
		entry := indexManager.findByISBN("9787111234567")
		if entry == nil {
			t.Error("通过ISBN查找失败")
		} else if entry.Title != "Go语言编程" {
			t.Errorf("期望标题 'Go语言编程', 得到 '%s'", entry.Title)
		}
	})

	t.Run("TestFindByTitleAuthor", func(t *testing.T) {
		entry := indexManager.findByTitleAuthor("Go语言编程", "张三")
		if entry == nil {
			t.Error("通过标题+作者查找失败")
		} else if entry.ISBN != "9787111234567" {
			t.Errorf("期望ISBN '9787111234567', 得到 '%s'", entry.ISBN)
		}
	})

	t.Run("TestFindByFileName", func(t *testing.T) {
		entries := indexManager.findByFileName("Go语言编程")
		if len(entries) != 1 {
			t.Errorf("期望找到1个条目, 得到 %d 个", len(entries))
		} else if entries[0].Author != "张三" {
			t.Errorf("期望作者 '张三', 得到 '%s'", entries[0].Author)
		}
	})

	// 测试智能查找
	t.Run("TestFindWithValidation", func(t *testing.T) {
		// 测试ISBN匹配
		testFile := EbookFile{
			Name:      "Go编程指南",  // 不同的文件名
			Path:      "/test/Go编程指南.pdf",
			Size:      1024000,
			Extension: ".pdf",
		}
		
		testBookInfo := &BookInfo{
			Title:  "Go编程指南",
			Author: "李四",
			ISBN:   "9787111234567", // 相同的ISBN
		}
		
		result := indexManager.FindWithValidation(testFile, testBookInfo)
		if result.Entry == nil {
			t.Error("智能查找失败：应该通过ISBN找到匹配项")
		} else if result.MatchType != "isbn" {
			t.Errorf("期望匹配类型 'isbn', 得到 '%s'", result.MatchType)
		} else if result.Confidence != 1.0 {
			t.Errorf("期望置信度 1.0, 得到 %.2f", result.Confidence)
		}
	})

	// 测试冲突检测
	t.Run("TestConflictDetection", func(t *testing.T) {
		// 添加另一个同名但不同内容的文件
		file2 := EbookFile{
			Name:      "Go语言编程",  // 相同文件名
			Path:      "/test/Go语言编程_v2.pdf",
			Size:      2048000,     // 不同大小
			Extension: ".pdf",
		}
		
		bookInfo2 := &BookInfo{
			Title:  "Go语言编程",
			Author: "王五",        // 不同作者
			ISBN:   "9787111234568", // 不同ISBN
		}
		
		result := indexManager.FindWithValidation(file2, bookInfo2)
		if len(result.Conflicts) == 0 {
			t.Error("应该检测到冲突")
		}
	})

	// 测试统计信息
	t.Run("TestPrintStats", func(t *testing.T) {
		// 这个测试主要确保PrintStats不会崩溃
		indexManager.PrintStats()
	})
}

func TestIndexKeyGeneration(t *testing.T) {
	generator := &IndexKeyGenerator{}
	
	file := EbookFile{
		Name:      "测试书籍",
		Size:      1024,
		Extension: ".pdf",
	}
	
	t.Run("TestISBNKey", func(t *testing.T) {
		bookInfo := &BookInfo{
			Title:  "测试书籍",
			Author: "测试作者",
			ISBN:   "978-7-111-23456-7",
		}
		
		key := generator.GeneratePrimaryKey(file, bookInfo)
		expected := "isbn:9787111234567"
		if key != expected {
			t.Errorf("期望主键 '%s', 得到 '%s'", expected, key)
		}
	})
	
	t.Run("TestTitleAuthorKey", func(t *testing.T) {
		bookInfo := &BookInfo{
			Title:  "测试书籍",
			Author: "测试作者",
			// 没有ISBN
		}
		
		key := generator.GeneratePrimaryKey(file, bookInfo)
		expected := "book:测试书籍:测试作者"
		if key != expected {
			t.Errorf("期望主键 '%s', 得到 '%s'", expected, key)
		}
	})
	
	t.Run("TestFileKey", func(t *testing.T) {
		// 没有书籍信息
		key := generator.GeneratePrimaryKey(file, nil)
		expected := "file:测试书籍:1024:.pdf"
		if key != expected {
			t.Errorf("期望主键 '%s', 得到 '%s'", expected, key)
		}
	})
}

func TestStringSimilarity(t *testing.T) {
	indexManager := &IndexManager{}
	
	testCases := []struct {
		s1       string
		s2       string
		expected float64
	}{
		{"Go语言编程", "Go语言编程", 1.0},
		{"Go语言编程", "go语言编程", 1.0}, // 大小写不敏感
		{"Go语言编程", "Java语言编程", 0.75}, // 部分匹配
		{"Go语言编程", "完全不同的书", 0.0},   // 完全不匹配
	}
	
	for _, tc := range testCases {
		similarity := indexManager.stringSimilarity(tc.s1, tc.s2)
		if similarity < tc.expected-0.1 || similarity > tc.expected+0.1 {
			t.Errorf("字符串 '%s' 和 '%s' 的相似度期望约 %.2f, 得到 %.2f", 
				tc.s1, tc.s2, tc.expected, similarity)
		}
	}
}
