package main

import (
	"fmt"
	"os"
	"path/filepath"

	"github.com/penwyp/hydra/apps/hydra/pkg/ebook"
)

func main() {
	fmt.Println("=== 增强索引功能演示 ===")
	
	// 创建临时目录
	tempDir, err := os.MkdirTemp("", "enhanced_index_demo")
	if err != nil {
		fmt.Printf("创建临时目录失败: %v\n", err)
		return
	}
	defer os.RemoveAll(tempDir)
	
	// 创建索引管理器
	indexManager := ebook.NewIndexManager(tempDir)
	
	// 加载索引
	if err := indexManager.LoadIndex(); err != nil {
		fmt.Printf("加载索引失败: %v\n", err)
		return
	}
	
	fmt.Println("\n1. 添加测试数据...")
	
	// 添加第一本书
	file1 := ebook.EbookFile{
		Name:      "Go语言编程",
		Path:      "/books/Go语言编程.pdf",
		Size:      1024000,
		Extension: ".pdf",
	}
	
	bookInfo1 := &ebook.BookInfo{
		Title:       "Go语言编程",
		Author:      "张三",
		ISBN:        "9787111234567",
		Publisher:   "机械工业出版社",
		PublishDate: "2023-01-01",
		Language:    "zh",
		Description: "这是一本关于Go语言编程的书籍，适合初学者和进阶开发者",
		Genre:       []string{"编程", "计算机", "Go语言"},
	}
	
	classification1 := &ebook.ClassificationResult{
		Category:   "应用学科·技术与编程",
		Confidence: 0.95,
		Reason:     "基于书名、作者和内容分析确定为编程类书籍",
	}
	
	indexManager.AddEntry(file1, bookInfo1, classification1, "/target/programming")
	
	// 添加第二本书
	file2 := ebook.EbookFile{
		Name:      "Python数据分析",
		Path:      "/books/Python数据分析.epub",
		Size:      2048000,
		Extension: ".epub",
	}
	
	bookInfo2 := &ebook.BookInfo{
		Title:       "Python数据分析",
		Author:      "李四",
		ISBN:        "9787111234568",
		Publisher:   "清华大学出版社",
		PublishDate: "2023-03-15",
		Language:    "zh",
		Description: "深入浅出讲解Python在数据分析领域的应用",
		Genre:       []string{"数据分析", "Python", "统计学"},
	}
	
	classification2 := &ebook.ClassificationResult{
		Category:   "应用学科·技术与编程",
		Confidence: 0.92,
		Reason:     "基于书名和内容分析确定为数据分析类编程书籍",
	}
	
	indexManager.AddEntry(file2, bookInfo2, classification2, "/target/programming")
	
	// 添加第三本书（同名但不同内容）
	file3 := ebook.EbookFile{
		Name:      "Go语言编程",  // 与第一本书同名
		Path:      "/books/Go语言编程_进阶版.pdf",
		Size:      3072000,     // 不同大小
		Extension: ".pdf",
	}
	
	bookInfo3 := &ebook.BookInfo{
		Title:       "Go语言编程进阶",
		Author:      "王五",      // 不同作者
		ISBN:        "9787111234569", // 不同ISBN
		Publisher:   "电子工业出版社",
		PublishDate: "2023-06-01",
		Language:    "zh",
		Description: "Go语言高级编程技巧和最佳实践",
		Genre:       []string{"编程", "Go语言", "高级"},
	}
	
	classification3 := &ebook.ClassificationResult{
		Category:   "应用学科·技术与编程",
		Confidence: 0.97,
		Reason:     "基于书名和高级内容分析确定为编程类书籍",
	}
	
	indexManager.AddEntry(file3, bookInfo3, classification3, "/target/programming")
	
	// 保存索引
	if err := indexManager.SaveIndex(); err != nil {
		fmt.Printf("保存索引失败: %v\n", err)
		return
	}
	
	fmt.Println("✓ 已添加3本书到索引")
	
	fmt.Println("\n2. 测试智能查找功能...")
	
	// 测试1: ISBN精确匹配
	fmt.Println("\n测试1: ISBN精确匹配")
	testFile1 := ebook.EbookFile{
		Name:      "Go编程手册",  // 不同的文件名
		Path:      "/test/Go编程手册.pdf",
		Size:      1000000,
		Extension: ".pdf",
	}
	
	testBookInfo1 := &ebook.BookInfo{
		Title:  "Go编程手册",
		Author: "未知作者",
		ISBN:   "9787111234567", // 与第一本书相同的ISBN
	}
	
	result1 := indexManager.FindWithValidation(testFile1, testBookInfo1)
	if result1.Entry != nil {
		fmt.Printf("✓ 通过ISBN找到匹配: %s (作者: %s, 匹配类型: %s, 置信度: %.2f)\n",
			result1.Entry.Title, result1.Entry.Author, result1.MatchType, result1.Confidence)
	} else {
		fmt.Println("✗ ISBN匹配失败")
	}
	
	// 测试2: 标题+作者匹配
	fmt.Println("\n测试2: 标题+作者匹配")
	testFile2 := ebook.EbookFile{
		Name:      "Python分析",  // 简化的文件名
		Path:      "/test/Python分析.epub",
		Size:      2000000,
		Extension: ".epub",
	}
	
	testBookInfo2 := &ebook.BookInfo{
		Title:  "Python数据分析",
		Author: "李四",
		// 没有ISBN
	}
	
	result2 := indexManager.FindWithValidation(testFile2, testBookInfo2)
	if result2.Entry != nil {
		fmt.Printf("✓ 通过标题+作者找到匹配: %s (ISBN: %s, 匹配类型: %s, 置信度: %.2f)\n",
			result2.Entry.Title, result2.Entry.ISBN, result2.MatchType, result2.Confidence)
	} else {
		fmt.Println("✗ 标题+作者匹配失败")
	}
	
	// 测试3: 冲突检测
	fmt.Println("\n测试3: 冲突检测")
	testFile3 := ebook.EbookFile{
		Name:      "Go语言编程",  // 与已有文件同名
		Path:      "/test/Go语言编程_新版.pdf",
		Size:      1500000,     // 不同大小
		Extension: ".pdf",
	}
	
	testBookInfo3 := &ebook.BookInfo{
		Title:  "Go语言编程新版",
		Author: "赵六",        // 不同作者
		// 没有ISBN
	}
	
	result3 := indexManager.FindWithValidation(testFile3, testBookInfo3)
	if len(result3.Conflicts) > 0 {
		fmt.Printf("✓ 检测到 %d 个冲突条目:\n", len(result3.Conflicts))
		for i, conflict := range result3.Conflicts {
			fmt.Printf("  冲突%d: %s (作者: %s, ISBN: %s)\n", 
				i+1, conflict.Title, conflict.Author, conflict.ISBN)
		}
	} else if result3.Entry != nil {
		fmt.Printf("✓ 找到匹配: %s (匹配类型: %s, 置信度: %.2f)\n",
			result3.Entry.Title, result3.MatchType, result3.Confidence)
	} else {
		fmt.Println("✓ 没有找到匹配项，需要进行新的分类")
	}
	
	// 测试4: 文件名匹配但需要验证
	fmt.Println("\n测试4: 文件名匹配验证")
	testFile4 := ebook.EbookFile{
		Name:      "Python数据分析",
		Path:      "/test/Python数据分析_副本.pdf", // 不同扩展名
		Size:      2048000,     // 相同大小
		Extension: ".pdf",      // 不同扩展名
	}
	
	result4 := indexManager.FindWithValidation(testFile4, nil)
	if result4.Entry != nil {
		fmt.Printf("✓ 通过文件名找到匹配: %s (匹配类型: %s, 置信度: %.2f)\n",
			result4.Entry.Title, result4.MatchType, result4.Confidence)
	} else if len(result4.Conflicts) > 0 {
		fmt.Printf("✓ 文件名匹配但存在冲突: %d 个冲突条目\n", len(result4.Conflicts))
	} else {
		fmt.Println("✓ 没有找到匹配项")
	}
	
	fmt.Println("\n3. 索引统计信息:")
	indexManager.PrintStats()
	
	fmt.Println("\n=== 演示完成 ===")
	fmt.Printf("索引文件保存在: %s\n", filepath.Join(tempDir, ".ebook_enhanced_index.json"))
}
