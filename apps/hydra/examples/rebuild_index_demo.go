package main

import (
	"fmt"
	"os"
	"path/filepath"

	"github.com/penwyp/hydra/apps/hydra/pkg/ebook"
)

func main() {
	fmt.Println("=== 重建索引功能演示 ===")
	
	// 创建临时目录结构
	tempDir, err := os.MkdirTemp("", "rebuild_index_demo")
	if err != nil {
		fmt.Printf("创建临时目录失败: %v\n", err)
		return
	}
	defer os.RemoveAll(tempDir)
	
	fmt.Printf("演示目录: %s\n", tempDir)
	
	// 创建分类目录结构
	categories := []string{
		"应用学科·技术与编程",
		"文学艺术·文学小说",
		"人文·历史文化",
	}
	
	// 创建示例文件
	testFiles := []struct {
		category string
		filename string
		content  string
	}{
		{"应用学科·技术与编程", "Go语言编程.pdf", "Go programming book content"},
		{"应用学科·技术与编程", "Python数据分析.epub", "Python data analysis content"},
		{"文学艺术·文学小说", "三体.txt", "Science fiction novel content"},
		{"文学艺术·文学小说", "红楼梦.pdf", "Classic Chinese literature"},
		{"人文·历史文化", "中国通史.pdf", "Chinese history book"},
	}
	
	fmt.Println("\n1. 创建测试目录结构和文件...")
	
	for _, category := range categories {
		categoryDir := filepath.Join(tempDir, category)
		if err := os.MkdirAll(categoryDir, 0755); err != nil {
			fmt.Printf("创建分类目录失败: %v\n", err)
			return
		}
	}
	
	for _, file := range testFiles {
		filePath := filepath.Join(tempDir, file.category, file.filename)
		if err := os.WriteFile(filePath, []byte(file.content), 0644); err != nil {
			fmt.Printf("创建测试文件失败: %v\n", err)
			return
		}
		fmt.Printf("  创建文件: %s/%s\n", file.category, file.filename)
	}
	
	fmt.Println("\n2. 创建索引管理器...")
	
	// 创建索引管理器
	indexManager := ebook.NewIndexManager(tempDir)
	
	// 加载索引（应该是空的）
	if err := indexManager.LoadIndex(); err != nil {
		fmt.Printf("加载索引失败: %v\n", err)
		return
	}
	
	fmt.Println("初始索引状态:")
	indexManager.PrintStats()
	
	fmt.Println("\n3. 模拟重建索引过程...")
	
	// 创建元数据检索器
	metadataRetriever := ebook.NewMetadataRetriever()
	
	var totalFiles, processedFiles int
	
	// 遍历目标目录重建索引
	err = filepath.Walk(tempDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		
		// 跳过目录和隐藏文件
		if info.IsDir() || info.Name()[0] == '.' {
			return nil
		}
		
		// 检查是否是电子书文件
		ext := filepath.Ext(info.Name())
		if !isEbookFile(ext) {
			return nil
		}
		
		totalFiles++
		
		// 确定分类（基于文件所在的目录）
		relPath, err := filepath.Rel(tempDir, path)
		if err != nil {
			fmt.Printf("无法获取相对路径: %v\n", err)
			return nil
		}
		
		// 获取分类名称（第一级目录名）
		pathParts := filepath.SplitList(relPath)
		if len(pathParts) < 1 {
			return nil
		}
		
		// 在Windows和Unix系统上路径分隔符不同，需要特殊处理
		category := filepath.Dir(relPath)
		if category == "." {
			return nil // 文件直接在根目录下
		}
		
		// 创建 EbookFile 对象
		nameWithoutExt := info.Name()[:len(info.Name())-len(ext)]
		file := ebook.EbookFile{
			Name:      nameWithoutExt,
			Path:      path,
			Size:      info.Size(),
			Extension: ext,
		}
		
		// 检索书本元数据
		bookInfo, err := metadataRetriever.RetrieveMetadataWithPath(file.Name, file.Path)
		if err != nil {
			// 使用基本信息
			bookInfo = &ebook.BookInfo{
				Title:  file.Name,
				Author: "",
			}
		}
		
		// 创建分类结果
		classification := &ebook.ClassificationResult{
			Category:   category,
			Confidence: 1.0,
			Reason:     "从目标目录结构重建",
		}
		
		// 添加到索引
		indexManager.AddEntry(file, bookInfo, classification, path)
		processedFiles++
		
		fmt.Printf("  处理文件: %s -> %s\n", file.Name, category)
		
		return nil
	})
	
	if err != nil {
		fmt.Printf("遍历目录失败: %v\n", err)
		return
	}
	
	// 保存索引
	if err := indexManager.SaveIndex(); err != nil {
		fmt.Printf("保存索引失败: %v\n", err)
		return
	}
	
	fmt.Printf("\n4. 重建完成，处理了 %d/%d 个文件\n", processedFiles, totalFiles)
	
	fmt.Println("\n5. 重建后的索引状态:")
	indexManager.PrintStats()
	
	fmt.Println("\n6. 测试智能查找功能...")
	
	// 测试查找功能
	testFile := ebook.EbookFile{
		Name:      "Go语言编程",
		Path:      "/test/Go语言编程.pdf",
		Size:      1024,
		Extension: ".pdf",
	}
	
	result := indexManager.FindWithValidation(testFile, nil)
	if result.Entry != nil {
		fmt.Printf("✓ 找到匹配: %s (分类: %s, 匹配类型: %s, 置信度: %.2f)\n",
			result.Entry.Title, result.Entry.Category, result.MatchType, result.Confidence)
	} else {
		fmt.Println("✗ 未找到匹配项")
	}
	
	fmt.Println("\n=== 演示完成 ===")
	fmt.Printf("索引文件位置: %s\n", filepath.Join(tempDir, ".ebook_enhanced_index.json"))
}

// isEbookFile 检查文件扩展名是否为电子书格式
func isEbookFile(ext string) bool {
	ebookExts := []string{".pdf", ".epub", ".mobi", ".azw", ".azw3", ".txt"}
	for _, ebookExt := range ebookExts {
		if ext == ebookExt {
			return true
		}
	}
	return false
}
