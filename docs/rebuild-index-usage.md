# 重建索引功能使用指南

## 概述

`--rebuild-index` 功能允许你基于目标目录中已经分类好的电子书文件重新构建索引。这在以下情况下非常有用：

1. **索引文件丢失或损坏**：当 `.ebook_enhanced_index.json` 文件丢失或损坏时
2. **手动整理后同步索引**：当你手动移动或重新分类了一些文件后
3. **升级索引格式**：当索引格式升级后需要重建
4. **清理索引数据**：当索引中有过时或错误的数据时

## 使用方法

### 基本用法

```bash
hydra ebook-classify -t /path/to/target --rebuild-index
```

### 参数说明

- `-t, --target`: 目标目录路径（必需）
- `--rebuild-index`: 启用重建索引模式
- `-l, --logLevel`: 日志级别（可选，默认为 info）

### 注意事项

1. **只需要目标目录**：重建索引时不需要指定源目录
2. **不需要 LLM API 密钥**：重建过程不调用 LLM，基于现有目录结构
3. **会覆盖现有索引**：重建过程会创建全新的索引文件
4. **支持元数据提取**：会尝试从文件中提取元数据信息

## 工作原理

### 1. 目录结构扫描

系统会递归扫描目标目录，识别以下结构：

```
/target/
├── 应用学科·技术与编程/
│   ├── Go语言编程.pdf
│   └── Python数据分析.epub
├── 文学艺术·文学小说/
│   ├── 三体.txt
│   └── 红楼梦.pdf
└── 人文·历史文化/
    └── 中国通史.pdf
```

### 2. 分类识别

- **分类名称**：基于文件所在的第一级子目录名称
- **文件识别**：支持 `.pdf`, `.epub`, `.mobi`, `.azw`, `.azw3`, `.txt` 格式
- **跳过规则**：忽略隐藏文件和非电子书文件

### 3. 元数据提取

对每个文件尝试提取元数据：

- **EPUB 文件**：提取标题、作者、ISBN、出版社等信息
- **PDF 文件**：提取文档属性中的元数据
- **其他格式**：使用文件名作为基本信息

### 4. 索引构建

创建增强索引结构：

- **主索引**：基于 ISBN、标题+作者或文件特征的复合键
- **辅助索引**：ISBN、标题+作者、文件名、哈希值的快速查找表
- **元数据存储**：完整的书籍信息和分类数据

## 使用示例

### 示例 1：基本重建

```bash
# 重建 /books/sorted 目录的索引
hydra ebook-classify -t /books/sorted --rebuild-index
```

输出示例：
```
2024-01-15 10:30:00 INFO 开始从目标目录重建索引 targetDir=/books/sorted
2024-01-15 10:30:01 INFO 重建索引进度 processed=100 total=150
2024-01-15 10:30:02 INFO 索引重建完成 totalFiles=150 processedFiles=148 errorFiles=2

=== 电子书分类索引统计 ===
索引版本: 2.0
创建时间: 2024-01-15 10:30:00
更新时间: 2024-01-15 10:30:02
总条目数: 148

分类统计:
  应用学科·技术与编程: 32 本
  文学艺术·文学小说: 45 本
  人文·历史文化: 25 本
  社会科学·商业经管: 28 本
  社会科学·心理学与自我成长: 18 本

元数据来源统计:
  file: 89 本
  api_with_metadata: 45 本
  api_with_isbn: 14 本

索引效率:
  ISBN索引: 14 条
  标题+作者索引: 59 条
  文件名索引: 148 条
```

### 示例 2：调试模式重建

```bash
# 使用调试日志级别查看详细过程
hydra ebook-classify -t /books/sorted --rebuild-index -l debug
```

### 示例 3：重建后验证

```bash
# 重建索引
hydra ebook-classify -t /books/sorted --rebuild-index

# 查看统计信息
hydra ebook-classify -t /books/sorted --show-stats
```

## 常见问题

### Q: 重建索引会影响现有文件吗？

A: 不会。重建索引只是重新扫描和记录文件信息，不会移动或修改任何文件。

### Q: 重建过程中出现错误怎么办？

A: 系统会跳过有问题的文件并继续处理其他文件。错误文件的数量会在最终报告中显示。

### Q: 重建索引需要多长时间？

A: 取决于文件数量和是否启用元数据提取。通常每秒可以处理几十到几百个文件。

### Q: 可以只重建部分目录的索引吗？

A: 当前版本会重建整个目标目录的索引。如果需要部分重建，可以临时移动文件到单独目录。

### Q: 重建后的索引与原索引有什么区别？

A: 重建的索引会：
- 使用最新的索引格式
- 重新提取文件元数据
- 清理过时或错误的条目
- 基于当前的目录结构确定分类

## 最佳实践

1. **定期备份索引**：在重建前备份现有索引文件
2. **验证目录结构**：确保目录结构符合预期的分类体系
3. **检查文件完整性**：确保文件没有损坏或权限问题
4. **监控重建过程**：使用调试日志查看详细过程
5. **验证重建结果**：重建后检查统计信息和抽样验证

## 相关命令

- `--show-stats`: 显示索引统计信息
- `--dry-run`: 试运行模式（不适用于重建索引）
- `-l debug`: 启用调试日志查看详细过程
